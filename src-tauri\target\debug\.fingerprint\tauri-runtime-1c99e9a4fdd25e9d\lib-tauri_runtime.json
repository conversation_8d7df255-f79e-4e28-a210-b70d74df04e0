{"rustc": 16591470773350601817, "features": "[\"macos-private-api\"]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 5850938158949623325, "deps": [[442785307232013896, "build_script_build", false, 10740832738883382486], [3150220818285335163, "url", false, 8378097199966561882], [4143744114649553716, "raw_window_handle", false, 3760476461987929444], [7606335748176206944, "dpi", false, 5020484559195214095], [9010263965687315507, "http", false, 12211119702532877820], [9689903380558560274, "serde", false, 8732212302608731374], [10806645703491011684, "thiserror", false, 1911401726845299826], [11050281405049894993, "tauri_utils", false, 15157767861222970160], [13116089016666501665, "windows", false, 4842511405203577448], [15367738274754116744, "serde_json", false, 6491859613021625460], [16727543399706004146, "cookie", false, 14287270555621874903]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-1c99e9a4fdd25e9d\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}