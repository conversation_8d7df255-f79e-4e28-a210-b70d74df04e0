"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { DollarSign, Plus, ArrowLeft, TrendingUp, TrendingDown, Wallet } from "lucide-react"
import Link from "next/link"

export default function TreasuryManagement() {
  const [transactions, setTransactions] = useState([])

  const [newTransaction, setNewTransaction] = useState({
    description: "",
    type: "",
    category: "",
    amount: "",
    reference: "",
  })

  const [accounts] = useState([
    {
      name: "الحساب التشغيلي الرئيسي",
      type: "جاري",
      balance: 0.0,
      currency: "د.ل",
    },
    {
      name: "حساب الرواتب",
      type: "جاري",
      balance: 0.0,
      currency: "د.ل",
    },
    {
      name: "صندوق الطوارئ",
      type: "توفير",
      balance: 0.0,
      currency: "د.ل",
    },
    {
      name: "صندوق المعدات",
      type: "توفير",
      balance: 0.0,
      currency: "د.ل",
    },
  ])

  const getTotalBalance = () => {
    return accounts.reduce((total, account) => total + account.balance, 0)
  }

  const getMonthlyIncome = () => {
    return transactions
      .filter((t) => t.type === "إيراد" && t.status === "مكتمل")
      .reduce((total, t) => total + t.amount, 0)
  }

  const getMonthlyExpenses = () => {
    return Math.abs(
      transactions.filter((t) => t.type === "مصروف" && t.status === "مكتمل").reduce((total, t) => total + t.amount, 0),
    )
  }

  const getPendingPayments = () => {
    return transactions.filter((t) => t.status === "معلق").reduce((total, t) => total + Math.abs(t.amount), 0)
  }

  const addTransaction = () => {
    const transaction = {
      id: transactions.length + 1,
      date: new Date().toISOString().split("T")[0],
      ...newTransaction,
      amount:
        newTransaction.type === "مصروف"
          ? -Math.abs(Number.parseFloat(newTransaction.amount))
          : Number.parseFloat(newTransaction.amount),
      status: "مكتمل",
    }
    setTransactions([transaction, ...transactions])
    setNewTransaction({
      description: "",
      type: "",
      category: "",
      amount: "",
      reference: "",
    })
  }

  const getCashFlow = () => {
    const income = getMonthlyIncome()
    const expenses = getMonthlyExpenses()
    return income - expenses
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center space-x-4 space-x-reverse">
            <Link href="/">
              <Button variant="outline" size="sm" className="border-primary-300 text-primary-700 hover:bg-primary-50">
                <ArrowLeft className="h-4 w-4 ml-2 rtl-flip" />
                العودة للوحة التحكم
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-primary-900">إدارة الخزينة</h1>
              <p className="text-gray-600">نظرة عامة مالية وإدارة التدفق النقدي بالدينار الليبي (د.ل)</p>
            </div>
          </div>

          <Dialog>
            <DialogTrigger asChild>
              <Button className="bg-secondary-600 hover:bg-secondary-700">
                <Plus className="h-4 w-4 ml-2" />
                إضافة معاملة
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle className="text-primary-900">إضافة معاملة جديدة</DialogTitle>
                <DialogDescription className="text-gray-600">
                  تسجيل معاملة مالية جديدة بالدينار الليبي (د.ل)
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div>
                  <Label htmlFor="description" className="text-primary-900">
                    الوصف
                  </Label>
                  <Input
                    id="description"
                    value={newTransaction.description}
                    onChange={(e) => setNewTransaction({ ...newTransaction, description: e.target.value })}
                    placeholder="وصف المعاملة"
                    className="border-primary-200 focus:border-primary-500"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="type" className="text-primary-900">
                      النوع
                    </Label>
                    <Select
                      value={newTransaction.type}
                      onValueChange={(value) => setNewTransaction({ ...newTransaction, type: value })}
                    >
                      <SelectTrigger className="border-primary-200 focus:border-primary-500">
                        <SelectValue placeholder="اختر النوع" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="إيراد">إيراد</SelectItem>
                        <SelectItem value="مصروف">مصروف</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="category" className="text-primary-900">
                      الفئة
                    </Label>
                    <Select
                      value={newTransaction.category}
                      onValueChange={(value) => setNewTransaction({ ...newTransaction, category: value })}
                    >
                      <SelectTrigger className="border-primary-200 focus:border-primary-500">
                        <SelectValue placeholder="اختر الفئة" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="مبيعات">مبيعات</SelectItem>
                        <SelectItem value="رواتب">رواتب</SelectItem>
                        <SelectItem value="مواد">مواد</SelectItem>
                        <SelectItem value="تكاليف عامة">تكاليف عامة</SelectItem>
                        <SelectItem value="معدات">معدات</SelectItem>
                        <SelectItem value="أخرى">أخرى</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="amount" className="text-primary-900">
                      المبلغ (د.ل)
                    </Label>
                    <Input
                      id="amount"
                      type="number"
                      step="0.01"
                      value={newTransaction.amount}
                      onChange={(e) => setNewTransaction({ ...newTransaction, amount: e.target.value })}
                      placeholder="1000.00"
                      className="border-primary-200 focus:border-primary-500"
                    />
                  </div>
                  <div>
                    <Label htmlFor="reference" className="text-primary-900">
                      المرجع
                    </Label>
                    <Input
                      id="reference"
                      value={newTransaction.reference}
                      onChange={(e) => setNewTransaction({ ...newTransaction, reference: e.target.value })}
                      placeholder="REF-2024-001"
                      className="border-primary-200 focus:border-primary-500"
                    />
                  </div>
                </div>
                <Button onClick={addTransaction} className="w-full bg-primary-600 hover:bg-primary-700">
                  إضافة المعاملة
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Financial Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="border-2 border-primary-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">إجمالي الرصيد</CardTitle>
              <Wallet className="h-4 w-4 text-primary-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary-900">{getTotalBalance().toLocaleString()} د.ل</div>
              <p className="text-xs text-gray-500">جميع الحسابات</p>
            </CardContent>
          </Card>

          <Card className="border-2 border-secondary-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">الإيرادات الشهرية</CardTitle>
              <TrendingUp className="h-4 w-4 text-secondary-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-secondary-900">{getMonthlyIncome().toLocaleString()} د.ل</div>
              <p className="text-xs text-gray-500">الشهر الحالي</p>
            </CardContent>
          </Card>

          <Card className="border-2 border-red-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">المصروفات الشهرية</CardTitle>
              <TrendingDown className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-900">{getMonthlyExpenses().toLocaleString()} د.ل</div>
              <p className="text-xs text-gray-500">الشهر الحالي</p>
            </CardContent>
          </Card>

          <Card className="border-2 border-primary-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">التدفق النقدي</CardTitle>
              <DollarSign className="h-4 w-4 text-primary-600" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getCashFlow() >= 0 ? "text-secondary-900" : "text-red-900"}`}>
                {getCashFlow().toLocaleString()} د.ل
              </div>
              <p className="text-xs text-gray-500">صافي شهري</p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          {/* Account Balances */}
          <Card className="lg:col-span-1 border-2 border-primary-200">
            <CardHeader className="bg-primary-50">
              <CardTitle className="text-primary-900">أرصدة الحسابات</CardTitle>
              <CardDescription className="text-gray-600">
                الأرصدة الحالية لجميع الحسابات بالدينار الليبي (د.ل)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 pt-6">
              {accounts.map((account, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 border-2 border-primary-100 rounded-lg"
                >
                  <div>
                    <p className="font-medium text-primary-900">{account.name}</p>
                    <p className="text-sm text-gray-500">{account.type}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-primary-900">
                      {account.balance.toLocaleString()} {account.currency}
                    </p>
                    <p className="text-sm text-gray-500">دينار ليبي</p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Recent Transactions */}
          <Card className="lg:col-span-2 border-2 border-secondary-200">
            <CardHeader className="bg-secondary-50">
              <CardTitle className="text-secondary-900">المعاملات الأخيرة</CardTitle>
              <CardDescription className="text-gray-600">آخر المعاملات المالية بالدينار الليبي (د.ل)</CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-primary-900">التاريخ</TableHead>
                    <TableHead className="text-primary-900">الوصف</TableHead>
                    <TableHead className="text-primary-900">الفئة</TableHead>
                    <TableHead className="text-primary-900">المبلغ (د.ل)</TableHead>
                    <TableHead className="text-primary-900">الحالة</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions.slice(0, 5).map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell className="text-gray-700">{transaction.date}</TableCell>
                      <TableCell className="font-medium text-primary-900">{transaction.description}</TableCell>
                      <TableCell className="text-gray-700">{transaction.category}</TableCell>
                      <TableCell
                        className={`font-medium ${transaction.amount >= 0 ? "text-secondary-600" : "text-red-600"}`}
                      >
                        {Math.abs(transaction.amount).toLocaleString()} د.ل
                      </TableCell>
                      <TableCell>
                        <Badge
                          className={
                            transaction.status === "مكتمل"
                              ? "bg-secondary-100 text-secondary-800"
                              : "bg-orange-100 text-orange-800"
                          }
                        >
                          {transaction.status}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>

        {/* Full Transactions Table */}
        <Card className="border-2 border-primary-200">
          <CardHeader className="bg-primary-50">
            <CardTitle className="text-primary-900">جميع المعاملات</CardTitle>
            <CardDescription className="text-gray-600">
              سجل المعاملات الكامل مع روابط الإنتاج بالدينار الليبي (د.ل)
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-primary-900">التاريخ</TableHead>
                  <TableHead className="text-primary-900">الوصف</TableHead>
                  <TableHead className="text-primary-900">النوع</TableHead>
                  <TableHead className="text-primary-900">الفئة</TableHead>
                  <TableHead className="text-primary-900">المبلغ (د.ل)</TableHead>
                  <TableHead className="text-primary-900">المرجع</TableHead>
                  <TableHead className="text-primary-900">الحالة</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell className="text-gray-700">{transaction.date}</TableCell>
                    <TableCell className="font-medium text-primary-900">{transaction.description}</TableCell>
                    <TableCell>
                      <Badge
                        className={
                          transaction.type === "إيراد"
                            ? "bg-secondary-100 text-secondary-800"
                            : "bg-red-100 text-red-800"
                        }
                      >
                        {transaction.type}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-gray-700">{transaction.category}</TableCell>
                    <TableCell
                      className={`font-medium ${transaction.amount >= 0 ? "text-secondary-600" : "text-red-600"}`}
                    >
                      {Math.abs(transaction.amount).toLocaleString()} د.ل
                    </TableCell>
                    <TableCell className="text-gray-600">{transaction.reference}</TableCell>
                    <TableCell>
                      <Badge
                        className={
                          transaction.status === "مكتمل"
                            ? "bg-secondary-100 text-secondary-800"
                            : "bg-orange-100 text-orange-800"
                        }
                      >
                        {transaction.status}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
