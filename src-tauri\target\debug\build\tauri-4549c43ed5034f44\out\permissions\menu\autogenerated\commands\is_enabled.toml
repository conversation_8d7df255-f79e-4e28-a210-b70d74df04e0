# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-is-enabled"
description = "Enables the is_enabled command without any pre-configured scope."
commands.allow = ["is_enabled"]

[[permission]]
identifier = "deny-is-enabled"
description = "Denies the is_enabled command without any pre-configured scope."
commands.deny = ["is_enabled"]
