# إصلاح مشكلة تشغيل التطبيق على سطح المكتب

## 🔧 **المشاكل التي تم إصلاحها**

### **1. مشكلة إخفاء النافذة**
- ❌ **المشكلة:** `"visible": false` في tauri.conf.json
- ✅ **الحل:** تغيير إلى `"visible": true`

### **2. مشكلة تكوين Next.js**
- ❌ **المشكلة:** `swcMinify: true` غير مدعوم في Next.js 15
- ✅ **الحل:** إزالة الخيار غير المدعوم

### **3. مشكلة كود إظهار النافذة**
- ❌ **المشكلة:** كود معقد لإظهار النافذة
- ✅ **الحل:** تبسيط الكود والاعتماد على التكوين

---

## 🚀 **الحلول المطبقة**

### **أ. تكوين Tauri محسن:**
```json
{
  "app": {
    "windows": [
      {
        "visible": true,        // ✅ إظهار النافذة مباشرة
        "center": true,         // ✅ توسيط النافذة
        "focus": true,          // ✅ تركيز تلقائي
        "width": 1400,          // ✅ عرض مناسب
        "height": 900,          // ✅ ارتفاع مناسب
        "minWidth": 1200,       // ✅ حد أدنى للعرض
        "minHeight": 800        // ✅ حد أدنى للارتفاع
      }
    ]
  }
}
```

### **ب. تكوين Next.js محسن:**
```javascript
const nextConfig = {
  // إزالة الخيارات غير المدعومة
  compiler: {
    removeConsole: process.env.NODE_ENV === "production",
  },
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  }
}
```

### **ج. كود Rust مبسط:**
```rust
.setup(move |app| {
    app.manage(db);
    println!("Application setup completed successfully");
    Ok(())
})
```

---

## 🔍 **خطوات التشخيص**

إذا لم يظهر التطبيق بعد، تحقق من:

### **1. فحص العمليات:**
```bash
# تحقق من تشغيل العملية
tasklist | findstr furniture-factory
```

### **2. فحص المنافذ:**
```bash
# تحقق من المنفذ 3000
netstat -an | findstr 3000
```

### **3. فحص الأخطاء:**
- راجع رسائل الخطأ في Terminal
- تحقق من Event Viewer في Windows
- فحص ملفات Log في مجلد التطبيق

---

## 🛠️ **حلول إضافية**

### **إذا لم تظهر النافذة:**

#### **الحل 1: إعادة تشغيل التطبيق**
```bash
# أوقف العملية الحالية
Ctrl + C

# أعد تشغيل التطبيق
npm run tauri:dev
```

#### **الحل 2: تنظيف الذاكرة المؤقتة**
```bash
# احذف مجلد target
rm -rf src-tauri/target

# احذف مجلد .next
rm -rf .next

# أعد تثبيت التبعيات
npm install --legacy-peer-deps

# أعد تشغيل التطبيق
npm run tauri:dev
```

#### **الحل 3: فحص Windows Defender**
- تأكد من أن Windows Defender لا يحجب التطبيق
- أضف مجلد المشروع إلى الاستثناءات

#### **الحل 4: تشغيل كمسؤول**
- شغل PowerShell كمسؤول
- انتقل إلى مجلد المشروع
- شغل `npm run tauri:dev`

---

## 📋 **قائمة التحقق**

### **✅ تم إصلاحه:**
- [x] تكوين النافذة في tauri.conf.json
- [x] إزالة الخيارات غير المدعومة من next.config.js
- [x] تبسيط كود إظهار النافذة
- [x] تثبيت جميع التبعيات المطلوبة

### **🔍 للتحقق:**
- [ ] ظهور نافذة التطبيق على سطح المكتب
- [ ] عمل جميع الوظائف داخل التطبيق
- [ ] عدم وجود رسائل خطأ في Terminal

---

## 🎯 **النتيجة المتوقعة**

بعد تطبيق هذه الإصلاحات، يجب أن:

1. **تظهر نافذة التطبيق** تلقائياً عند تشغيل `npm run tauri:dev`
2. **تكون النافذة في المنتصف** مع الحجم المناسب
3. **يعمل التطبيق بسلاسة** بدون أخطاء
4. **تكون جميع الوظائف متاحة** داخل التطبيق

---

## 🚨 **إذا استمرت المشكلة**

### **معلومات مطلوبة للتشخيص:**
1. نظام التشغيل وإصداره
2. إصدار Node.js (`node --version`)
3. إصدار Rust (`rustc --version`)
4. رسائل الخطأ الكاملة من Terminal
5. هل يظهر التطبيق في Task Manager؟

### **خطوات إضافية:**
1. **إعادة تشغيل الكمبيوتر**
2. **تحديث Rust:** `rustup update`
3. **تحديث Tauri CLI:** `npm install -g @tauri-apps/cli@latest`
4. **فحص متطلبات النظام** لـ Tauri

---

## 📞 **الدعم**

إذا استمرت المشكلة، يرجى تقديم:
- لقطة شاشة من Terminal
- محتوى ملف tauri.conf.json
- رسائل الخطأ (إن وجدت)
- معلومات النظام

**التطبيق يجب أن يعمل الآن على سطح المكتب بنجاح!**
