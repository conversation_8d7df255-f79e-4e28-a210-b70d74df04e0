"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { CreditCard, Plus, ArrowLeft, FileText, Clock, CheckCircle, AlertCircle } from "lucide-react"
import Link from "next/link"

export default function PaymentsManagement() {
  const [projects, setProjects] = useState([])

  const [newProject, setNewProject] = useState({
    projectName: "",
    clientName: "",
    totalAmount: "",
    startDate: "",
    expectedCompletion: "",
  })

  const [selectedProject, setSelectedProject] = useState(null)
  const [showInvoice, setShowInvoice] = useState(false)
  const [invoiceType, setInvoiceType] = useState("initial")

  const getStatusColor = (status) => {
    switch (status) {
      case "مكتمل":
        return "bg-secondary-100 text-secondary-800"
      case "في الإنتاج":
        return "bg-primary-100 text-primary-800"
      case "في انتظار الموافقة":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPaymentStatusColor = (status) => {
    switch (status) {
      case "مكتمل":
        return "bg-secondary-100 text-secondary-800"
      case "تم دفع الدفعة الأولى":
        return "bg-primary-100 text-primary-800"
      case "في انتظار الدفعة الأولى":
        return "bg-orange-100 text-orange-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const addProject = () => {
    const totalAmount = Number.parseFloat(newProject.totalAmount)
    const project = {
      id: projects.length + 1,
      ...newProject,
      totalAmount,
      firstPayment: totalAmount * 0.7,
      finalPayment: totalAmount * 0.3,
      status: "في انتظار الموافقة",
      paymentStatus: "في انتظار الدفعة الأولى",
      firstInvoiceDate: null,
      finalInvoiceDate: null,
    }
    setProjects([...projects, project])
    setNewProject({
      projectName: "",
      clientName: "",
      totalAmount: "",
      startDate: "",
      expectedCompletion: "",
    })
  }

  const generateInvoice = (project, type) => {
    setSelectedProject(project)
    setInvoiceType(type)
    setShowInvoice(true)
  }

  const updatePaymentStatus = (projectId, newStatus, newPaymentStatus) => {
    setProjects(
      projects.map((project) =>
        project.id === projectId
          ? {
              ...project,
              status: newStatus,
              paymentStatus: newPaymentStatus,
              firstInvoiceDate:
                newPaymentStatus === "تم دفع الدفعة الأولى"
                  ? new Date().toISOString().split("T")[0]
                  : project.firstInvoiceDate,
              finalInvoiceDate:
                newPaymentStatus === "مكتمل" ? new Date().toISOString().split("T")[0] : project.finalInvoiceDate,
            }
          : project,
      ),
    )
  }

  const getTotalRevenue = () => {
    return projects
      .filter((p) => p.paymentStatus === "مكتمل")
      .reduce((total, project) => total + project.totalAmount, 0)
  }

  const getPendingFirstPayments = () => {
    return projects
      .filter((p) => p.paymentStatus === "في انتظار الدفعة الأولى")
      .reduce((total, project) => total + project.firstPayment, 0)
  }

  const getPendingFinalPayments = () => {
    return projects
      .filter((p) => p.paymentStatus === "تم دفع الدفعة الأولى")
      .reduce((total, project) => total + project.finalPayment, 0)
  }

  const InvoiceModal = () => {
    if (!selectedProject) return null

    const currentDate = new Date().toLocaleDateString("ar-SA")

    return (
      <Dialog open={showInvoice} onOpenChange={setShowInvoice}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-center text-2xl text-primary-900">
              {invoiceType === "initial" && "فاتورة الدفعة الأولى"}
              {invoiceType === "progress" && "فاتورة تقدم الإنتاج"}
              {invoiceType === "final" && "الفاتورة النهائية"}
            </DialogTitle>
          </DialogHeader>

          <div className="bg-white p-8 border-2 border-primary-200 rounded-lg">
            {/* Header */}
            <div className="flex justify-between items-start mb-8">
              <div>
                <h1 className="text-3xl font-bold text-primary-900">اتش قروب</h1>
                <p className="text-gray-600">مصنع الأثاث المخصص</p>
                <p className="text-sm text-gray-500">طرابلس، ليبيا</p>
                <p className="text-sm text-gray-500">هاتف: +218 21 123 4567</p>
              </div>
              <div className="text-left">
                <p className="text-lg font-semibold text-primary-900">
                  فاتورة رقم: INV-{selectedProject.id}-
                  {invoiceType === "initial" ? "01" : invoiceType === "progress" ? "02" : "03"}
                </p>
                <p className="text-gray-600">التاريخ: {currentDate}</p>
              </div>
            </div>

            {/* Client Info */}
            <div className="mb-8 p-4 bg-gray-50 rounded-lg">
              <h3 className="font-semibold text-primary-900 mb-2">معلومات العميل:</h3>
              <p className="text-gray-700">{selectedProject.clientName}</p>
              <p className="text-gray-600">المشروع: {selectedProject.projectName}</p>
            </div>

            {/* Invoice Details */}
            <div className="mb-8">
              {invoiceType === "initial" && (
                <div>
                  <h3 className="font-semibold text-primary-900 mb-4">تفاصيل الدفعة الأولى (70%)</h3>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>الوصف</TableHead>
                        <TableHead>المبلغ الإجمالي</TableHead>
                        <TableHead>النسبة</TableHead>
                        <TableHead>المبلغ المطلوب</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell>{selectedProject.projectName}</TableCell>
                        <TableCell>{selectedProject.totalAmount.toLocaleString()} د.ل</TableCell>
                        <TableCell>70%</TableCell>
                        <TableCell className="font-bold text-primary-900">
                          {selectedProject.firstPayment.toLocaleString()} د.ل
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                  <div className="mt-4 p-4 bg-primary-50 rounded-lg">
                    <p className="text-sm text-primary-800">
                      <strong>ملاحظة:</strong> هذا المبلغ مطلوب للموافقة على المشروع وبدء عملية الإنتاج. المبلغ المتبقي
                      (30%) مستحق عند الانتهاء من المشروع. جميع المبالغ بالدينار الليبي.
                    </p>
                  </div>
                </div>
              )}

              {invoiceType === "final" && (
                <div>
                  <h3 className="font-semibold text-primary-900 mb-4">الفاتورة النهائية</h3>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>البيان</TableHead>
                        <TableHead>المبلغ</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell>المبلغ الإجمالي للمشروع</TableCell>
                        <TableCell>{selectedProject.totalAmount.toLocaleString()} د.ل</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>الدفعة الأولى المدفوعة (70%)</TableCell>
                        <TableCell className="text-secondary-600">
                          -{selectedProject.firstPayment.toLocaleString()} د.ل
                        </TableCell>
                      </TableRow>
                      <TableRow className="border-t-2 border-primary-200">
                        <TableCell className="font-bold">المبلغ المتبقي المطلوب (30%)</TableCell>
                        <TableCell className="font-bold text-primary-900">
                          {selectedProject.finalPayment.toLocaleString()} د.ل
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>

                  <div className="mt-6 p-4 bg-secondary-50 rounded-lg">
                    <h4 className="font-medium text-secondary-900 mb-2">تفاصيل المنتج النهائي:</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p>
                          <strong>اسم المشروع:</strong> {selectedProject.projectName}
                        </p>
                        <p>
                          <strong>تاريخ البدء:</strong> {selectedProject.startDate}
                        </p>
                        <p>
                          <strong>تاريخ الإنجاز:</strong> {selectedProject.expectedCompletion}
                        </p>
                      </div>
                      <div>
                        <p>
                          <strong>المواد المستخدمة:</strong> خشب البلوط الطبيعي
                        </p>
                        <p>
                          <strong>التشطيب:</strong> طلاء لامع مقاوم للخدش
                        </p>
                        <p>
                          <strong>الضمان:</strong> سنتان شاملة
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Payment Terms */}
            <div className="border-t-2 border-primary-200 pt-4">
              <h4 className="font-semibold text-primary-900 mb-2">شروط الدفع:</h4>
              <p className="text-sm text-gray-600">
                {invoiceType === "initial" &&
                  "الدفع مطلوب خلال 7 أيام من تاريخ الفاتورة لبدء الإنتاج. جميع المبالغ بالدينار الليبي."}
                {invoiceType === "final" &&
                  "الدفع مطلوب عند التسليم. يتم التسليم بعد استلام المبلغ كاملاً. جميع المبالغ بالدينار الليبي."}
              </p>
            </div>

            {/* Footer */}
            <div className="mt-8 text-center text-sm text-gray-500">
              <p>شكراً لاختياركم اتش قروب لأثاثكم المخصص</p>
              <p>للاستفسارات: <EMAIL> | +218 21 123 4567</p>
            </div>
          </div>

          <div className="flex justify-end space-x-2 space-x-reverse mt-4">
            <Button variant="outline" onClick={() => setShowInvoice(false)}>
              إغلاق
            </Button>
            <Button className="bg-primary-600 hover:bg-primary-700">
              <FileText className="h-4 w-4 ml-2" />
              طباعة الفاتورة
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center space-x-4 space-x-reverse">
            <Link href="/">
              <Button variant="outline" size="sm" className="border-primary-300 text-primary-700 hover:bg-primary-50">
                <ArrowLeft className="h-4 w-4 ml-2 rtl-flip" />
                العودة للوحة التحكم
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-primary-900">نظام المدفوعات</h1>
              <p className="text-gray-600">إدارة الفواتير والمدفوعات المتدرجة بالدينار الليبي</p>
            </div>
          </div>

          <Dialog>
            <DialogTrigger asChild>
              <Button className="bg-secondary-600 hover:bg-secondary-700">
                <Plus className="h-4 w-4 ml-2" />
                مشروع جديد
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle className="text-primary-900">إضافة مشروع جديد</DialogTitle>
                <DialogDescription className="text-gray-600">أدخل تفاصيل المشروع الجديد</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div>
                  <Label htmlFor="projectName" className="text-primary-900">
                    اسم المشروع
                  </Label>
                  <Input
                    id="projectName"
                    value={newProject.projectName}
                    onChange={(e) => setNewProject({ ...newProject, projectName: e.target.value })}
                    placeholder="مثال: طقم طاولة طعام مخصص"
                    className="border-primary-200 focus:border-primary-500"
                  />
                </div>
                <div>
                  <Label htmlFor="clientName" className="text-primary-900">
                    اسم العميل
                  </Label>
                  <Input
                    id="clientName"
                    value={newProject.clientName}
                    onChange={(e) => setNewProject({ ...newProject, clientName: e.target.value })}
                    placeholder="اسم العميل أو الشركة"
                    className="border-primary-200 focus:border-primary-500"
                  />
                </div>
                <div>
                  <Label htmlFor="totalAmount" className="text-primary-900">
                    المبلغ الإجمالي (د.ل)
                  </Label>
                  <Input
                    id="totalAmount"
                    type="number"
                    step="0.01"
                    value={newProject.totalAmount}
                    onChange={(e) => setNewProject({ ...newProject, totalAmount: e.target.value })}
                    placeholder="3750.00"
                    className="border-primary-200 focus:border-primary-500"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="startDate" className="text-primary-900">
                      تاريخ البدء
                    </Label>
                    <Input
                      id="startDate"
                      type="date"
                      value={newProject.startDate}
                      onChange={(e) => setNewProject({ ...newProject, startDate: e.target.value })}
                      className="border-primary-200 focus:border-primary-500"
                    />
                  </div>
                  <div>
                    <Label htmlFor="expectedCompletion" className="text-primary-900">
                      تاريخ الإنجاز المتوقع
                    </Label>
                    <Input
                      id="expectedCompletion"
                      type="date"
                      value={newProject.expectedCompletion}
                      onChange={(e) => setNewProject({ ...newProject, expectedCompletion: e.target.value })}
                      className="border-primary-200 focus:border-primary-500"
                    />
                  </div>
                </div>
                <Button onClick={addProject} className="w-full bg-primary-600 hover:bg-primary-700">
                  إضافة المشروع
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="border-2 border-primary-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">إجمالي المشاريع</CardTitle>
              <CreditCard className="h-4 w-4 text-primary-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary-900">{projects.length}</div>
              <p className="text-xs text-gray-500">مشاريع نشطة</p>
            </CardContent>
          </Card>

          <Card className="border-2 border-secondary-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">الإيرادات المحققة</CardTitle>
              <CheckCircle className="h-4 w-4 text-secondary-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-secondary-900">{getTotalRevenue().toLocaleString()} د.ل</div>
              <p className="text-xs text-gray-500">مدفوعات مكتملة</p>
            </CardContent>
          </Card>

          <Card className="border-2 border-orange-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">دفعات أولى معلقة</CardTitle>
              <Clock className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-900">{getPendingFirstPayments().toLocaleString()} د.ل</div>
              <p className="text-xs text-gray-500">في انتظار الدفع</p>
            </CardContent>
          </Card>

          <Card className="border-2 border-primary-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">دفعات نهائية معلقة</CardTitle>
              <AlertCircle className="h-4 w-4 text-primary-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary-900">
                {getPendingFinalPayments().toLocaleString()} د.ل
              </div>
              <p className="text-xs text-gray-500">عند الإنجاز</p>
            </CardContent>
          </Card>
        </div>

        {/* Projects Table */}
        <Card className="border-2 border-primary-200">
          <CardHeader className="bg-primary-50">
            <CardTitle className="text-primary-900">إدارة المشاريع والمدفوعات</CardTitle>
            <CardDescription className="text-gray-600">
              نظام المدفوعات المتدرجة للمشاريع بالدينار الليبي
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-primary-900">المشروع</TableHead>
                  <TableHead className="text-primary-900">العميل</TableHead>
                  <TableHead className="text-primary-900">المبلغ الإجمالي</TableHead>
                  <TableHead className="text-primary-900">الدفعة الأولى (70%)</TableHead>
                  <TableHead className="text-primary-900">الدفعة النهائية (30%)</TableHead>
                  <TableHead className="text-primary-900">حالة المشروع</TableHead>
                  <TableHead className="text-primary-900">حالة الدفع</TableHead>
                  <TableHead className="text-primary-900">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {projects.map((project) => (
                  <TableRow key={project.id}>
                    <TableCell className="font-medium text-primary-900">{project.projectName}</TableCell>
                    <TableCell className="text-gray-700">{project.clientName}</TableCell>
                    <TableCell className="font-medium">{project.totalAmount.toLocaleString()} د.ل</TableCell>
                    <TableCell className="text-secondary-700">{project.firstPayment.toLocaleString()} د.ل</TableCell>
                    <TableCell className="text-primary-700">{project.finalPayment.toLocaleString()} د.ل</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(project.status)}>{project.status}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getPaymentStatusColor(project.paymentStatus)}>{project.paymentStatus}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2 space-x-reverse">
                        {project.paymentStatus === "في انتظار الدفعة الأولى" && (
                          <>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => generateInvoice(project, "initial")}
                              className="border-primary-300 text-primary-700 hover:bg-primary-50"
                            >
                              فاتورة أولى
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => updatePaymentStatus(project.id, "في الإنتاج", "تم دفع الدفعة الأولى")}
                              className="bg-secondary-600 hover:bg-secondary-700"
                            >
                              تأكيد الدفع
                            </Button>
                          </>
                        )}
                        {project.paymentStatus === "تم دفع الدفعة الأولى" && (
                          <>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => generateInvoice(project, "final")}
                              className="border-secondary-300 text-secondary-700 hover:bg-secondary-50"
                            >
                              فاتورة نهائية
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => updatePaymentStatus(project.id, "مكتمل", "مكتمل")}
                              className="bg-secondary-600 hover:bg-secondary-700"
                            >
                              إنجاز المشروع
                            </Button>
                          </>
                        )}
                        {project.paymentStatus === "مكتمل" && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => generateInvoice(project, "final")}
                            className="border-primary-300 text-primary-700 hover:bg-primary-50"
                          >
                            عرض الفاتورة
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <InvoiceModal />
      </div>
    </div>
  )
}
