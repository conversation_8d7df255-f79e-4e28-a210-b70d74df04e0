use tauri::State;
use crate::{database::Database, models::*, error::Result};
use sqlx::Row;

#[tauri::command]
pub async fn test_database_connection(db: State<'_, Database>) -> Result<String> {
    // Test basic connection
    let _result = sqlx::query("SELECT 1 as test")
        .fetch_one(&db.pool)
        .await?;

    Ok("Database connection successful".to_string())
}

#[tauri::command]
pub async fn test_database_tables(db: State<'_, Database>) -> Result<Vec<String>> {
    let mut tables = Vec::new();

    // Get all table names
    let rows = sqlx::query("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        .fetch_all(&db.pool)
        .await?;

    for row in rows {
        let table_name: String = row.get("name");
        tables.push(table_name);
    }

    Ok(tables)
}

#[tauri::command]
pub async fn test_data_insertion(db: State<'_, Database>) -> Result<String> {
    let mut results = Vec::new();

    // Test Materials
    let materials_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM materials")
        .fetch_one(&db.pool)
        .await?;
    results.push(format!("Materials: {} records", materials_count));

    // Test Customers
    let customers_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM customers")
        .fetch_one(&db.pool)
        .await?;
    results.push(format!("Customers: {} records", customers_count));

    // Test Employees
    let employees_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM employees")
        .fetch_one(&db.pool)
        .await?;
    results.push(format!("Employees: {} records", employees_count));

    // Test Projects
    let projects_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM projects")
        .fetch_one(&db.pool)
        .await?;
    results.push(format!("Projects: {} records", projects_count));

    // Test Payments
    let payments_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM payments")
        .fetch_one(&db.pool)
        .await?;
    results.push(format!("Payments: {} records", payments_count));

    // Test Transactions
    let transactions_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM transactions")
        .fetch_one(&db.pool)
        .await?;
    results.push(format!("Transactions: {} records", transactions_count));

    // Test Activities
    let activities_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM activities")
        .fetch_one(&db.pool)
        .await?;
    results.push(format!("Activities: {} records", activities_count));

    // Test Accounts
    let accounts_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM accounts")
        .fetch_one(&db.pool)
        .await?;
    results.push(format!("Accounts: {} records", accounts_count));

    Ok(results.join(", "))
}

#[tauri::command]
pub async fn test_crud_operations(db: State<'_, Database>) -> Result<String> {
    let mut results = Vec::new();

    // Test CREATE - Add a test material
    let test_material = CreateMaterial {
        name: "Test Material".to_string(),
        category: "Test Category".to_string(),
        current_stock: 100.0,
        min_stock: 10.0,
        max_stock: 500.0,
        unit: "kg".to_string(),
        cost_per_unit: 25.0,
        supplier: "Test Supplier".to_string(),
    };

    let created_material = sqlx::query_as::<_, Material>(
        r#"
        INSERT INTO materials (name, category, current_stock, min_stock, max_stock, unit, cost_per_unit, supplier)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        RETURNING *
        "#
    )
    .bind(&test_material.name)
    .bind(&test_material.category)
    .bind(test_material.current_stock)
    .bind(test_material.min_stock)
    .bind(test_material.max_stock)
    .bind(&test_material.unit)
    .bind(test_material.cost_per_unit)
    .bind(&test_material.supplier)
    .fetch_one(&db.pool)
    .await?;

    results.push(format!("CREATE: Material ID {} created", created_material.id));

    // Test READ
    let read_material = sqlx::query_as::<_, Material>(
        "SELECT * FROM materials WHERE id = ?"
    )
    .bind(created_material.id)
    .fetch_one(&db.pool)
    .await?;

    results.push(format!("READ: Material '{}' found", read_material.name));

    // Test UPDATE
    sqlx::query(
        "UPDATE materials SET current_stock = ? WHERE id = ?"
    )
    .bind(150.0)
    .bind(created_material.id)
    .execute(&db.pool)
    .await?;

    let updated_material = sqlx::query_as::<_, Material>(
        "SELECT * FROM materials WHERE id = ?"
    )
    .bind(created_material.id)
    .fetch_one(&db.pool)
    .await?;

    results.push(format!("UPDATE: Stock updated to {}", updated_material.current_stock));

    // Test DELETE
    let delete_result = sqlx::query("DELETE FROM materials WHERE id = ?")
        .bind(created_material.id)
        .execute(&db.pool)
        .await?;

    results.push(format!("DELETE: {} row(s) deleted", delete_result.rows_affected()));

    Ok(results.join(", "))
}

#[tauri::command]
pub async fn test_foreign_keys(db: State<'_, Database>) -> Result<String> {
    let mut results = Vec::new();

    // Test if foreign key constraints are working
    // Try to get a project with customer info
    let project_with_customer = sqlx::query(
        r#"
        SELECT p.name as project_name, c.name as customer_name
        FROM projects p
        JOIN customers c ON p.customer_id = c.id
        LIMIT 1
        "#
    )
    .fetch_optional(&db.pool)
    .await?;

    if let Some(row) = project_with_customer {
        let project_name: String = row.get("project_name");
        let customer_name: String = row.get("customer_name");
        results.push(format!("Foreign Key Test: Project '{}' belongs to '{}'", project_name, customer_name));
    } else {
        results.push("No projects with customers found".to_string());
    }

    Ok(results.join(", "))
}

#[tauri::command]
pub async fn get_database_info(db: State<'_, Database>) -> Result<String> {
    let mut info = Vec::new();

    // Get database file size
    let db_path = std::env::current_dir()
        .unwrap()
        .join("data")
        .join("furniture_factory.db");

    if let Ok(metadata) = std::fs::metadata(&db_path) {
        info.push(format!("Database size: {} bytes", metadata.len()));
    }

    // Get SQLite version
    let version: String = sqlx::query_scalar("SELECT sqlite_version()")
        .fetch_one(&db.pool)
        .await?;
    info.push(format!("SQLite version: {}", version));

    // Get database schema version
    let user_version: i64 = sqlx::query_scalar("PRAGMA user_version")
        .fetch_one(&db.pool)
        .await?;
    info.push(format!("Schema version: {}", user_version));

    Ok(info.join(", "))
}
