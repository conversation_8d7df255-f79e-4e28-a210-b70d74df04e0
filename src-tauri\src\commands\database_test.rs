use tauri::State;
use crate::{database::Database, models::*, error::Result};
use sqlx::Row;
use chrono::Utc;

#[tauri::command]
pub async fn test_database_connection(db: State<'_, Database>) -> Result<String> {
    // Test basic connection
    let _result = sqlx::query("SELECT 1 as test")
        .fetch_one(&db.pool)
        .await?;

    Ok("Database connection successful".to_string())
}

#[tauri::command]
pub async fn test_database_tables(db: State<'_, Database>) -> Result<Vec<String>> {
    let mut tables = Vec::new();

    // Get all table names
    let rows = sqlx::query("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        .fetch_all(&db.pool)
        .await?;

    for row in rows {
        let table_name: String = row.get("name");
        tables.push(table_name);
    }

    Ok(tables)
}

#[tauri::command]
pub async fn test_data_insertion(db: State<'_, Database>) -> Result<String> {
    let mut results = Vec::new();

    // Test Materials
    let materials_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM materials")
        .fetch_one(&db.pool)
        .await?;
    results.push(format!("Materials: {} records", materials_count));

    // Test Customers
    let customers_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM customers")
        .fetch_one(&db.pool)
        .await?;
    results.push(format!("Customers: {} records", customers_count));

    // Test Employees
    let employees_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM employees")
        .fetch_one(&db.pool)
        .await?;
    results.push(format!("Employees: {} records", employees_count));

    // Test Projects
    let projects_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM projects")
        .fetch_one(&db.pool)
        .await?;
    results.push(format!("Projects: {} records", projects_count));

    // Test Payments
    let payments_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM payments")
        .fetch_one(&db.pool)
        .await?;
    results.push(format!("Payments: {} records", payments_count));

    // Test Transactions
    let transactions_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM transactions")
        .fetch_one(&db.pool)
        .await?;
    results.push(format!("Transactions: {} records", transactions_count));

    // Test Activities
    let activities_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM activities")
        .fetch_one(&db.pool)
        .await?;
    results.push(format!("Activities: {} records", activities_count));

    // Test Accounts
    let accounts_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM accounts")
        .fetch_one(&db.pool)
        .await?;
    results.push(format!("Accounts: {} records", accounts_count));

    Ok(results.join(", "))
}

#[tauri::command]
pub async fn test_crud_operations(db: State<'_, Database>) -> Result<String> {
    let mut results = Vec::new();

    // Add a unique identifier to avoid conflicts
    let timestamp = chrono::Utc::now().timestamp();

    // Test CREATE - Add a test material with unique name
    let test_material = CreateMaterial {
        name: format!("Test Material {}", timestamp),
        category: "Test Category".to_string(),
        current_stock: 100.0,
        min_stock: 10.0,
        max_stock: 500.0,
        unit: "kg".to_string(),
        cost_per_unit: 25.0,
        supplier: "Test Supplier".to_string(),
    };

    // Insert the test material
    let insert_result = sqlx::query(
        r#"
        INSERT INTO materials (name, category, current_stock, min_stock, max_stock, unit, cost_per_unit, supplier)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        "#
    )
    .bind(&test_material.name)
    .bind(&test_material.category)
    .bind(test_material.current_stock)
    .bind(test_material.min_stock)
    .bind(test_material.max_stock)
    .bind(&test_material.unit)
    .bind(test_material.cost_per_unit)
    .bind(&test_material.supplier)
    .execute(&db.pool)
    .await?;

    let material_id = insert_result.last_insert_rowid();

    // Get the created material
    let created_material = sqlx::query_as::<_, Material>(
        "SELECT * FROM materials WHERE id = ?"
    )
    .bind(material_id)
    .fetch_one(&db.pool)
    .await?;

    results.push(format!("CREATE: Material ID {} created with name '{}'", created_material.id, created_material.name));

    // Test READ
    let read_material = sqlx::query_as::<_, Material>(
        "SELECT * FROM materials WHERE id = ?"
    )
    .bind(created_material.id)
    .fetch_one(&db.pool)
    .await?;

    if read_material.name == created_material.name {
        results.push(format!("READ: Material '{}' found successfully", read_material.name));
    } else {
        results.push(format!("READ: Error - Expected '{}', got '{}'", created_material.name, read_material.name));
    }

    // Test UPDATE
    let new_stock = 150.0;
    let update_result = sqlx::query(
        "UPDATE materials SET current_stock = ? WHERE id = ?"
    )
    .bind(new_stock)
    .bind(created_material.id)
    .execute(&db.pool)
    .await?;

    if update_result.rows_affected() > 0 {
        let updated_material = sqlx::query_as::<_, Material>(
            "SELECT * FROM materials WHERE id = ?"
        )
        .bind(created_material.id)
        .fetch_one(&db.pool)
        .await?;

        if (updated_material.current_stock - new_stock).abs() < 0.01 {
            results.push(format!("UPDATE: Stock successfully updated from {} to {}", created_material.current_stock, updated_material.current_stock));
        } else {
            results.push(format!("UPDATE: Error - Expected {}, got {}", new_stock, updated_material.current_stock));
        }
    } else {
        results.push("UPDATE: Error - No rows affected".to_string());
    }

    // Test DELETE
    let delete_result = sqlx::query("DELETE FROM materials WHERE id = ?")
        .bind(created_material.id)
        .execute(&db.pool)
        .await?;

    if delete_result.rows_affected() > 0 {
        // Verify deletion
        let verify_delete = sqlx::query_as::<_, Material>(
            "SELECT * FROM materials WHERE id = ?"
        )
        .bind(created_material.id)
        .fetch_optional(&db.pool)
        .await?;

        if verify_delete.is_none() {
            results.push(format!("DELETE: Material ID {} successfully deleted", created_material.id));
        } else {
            results.push("DELETE: Error - Material still exists after deletion".to_string());
        }
    } else {
        results.push("DELETE: Error - No rows affected".to_string());
    }

    Ok(results.join(" | "))
}

#[tauri::command]
pub async fn test_foreign_keys(db: State<'_, Database>) -> Result<String> {
    let mut results = Vec::new();

    // Test if foreign key constraints are working
    // Try to get a project with customer info
    let project_with_customer = sqlx::query(
        r#"
        SELECT p.name as project_name, c.name as customer_name
        FROM projects p
        JOIN customers c ON p.customer_id = c.id
        LIMIT 1
        "#
    )
    .fetch_optional(&db.pool)
    .await?;

    if let Some(row) = project_with_customer {
        let project_name: String = row.get("project_name");
        let customer_name: String = row.get("customer_name");
        results.push(format!("Foreign Key Test: Project '{}' belongs to '{}'", project_name, customer_name));
    } else {
        results.push("No projects with customers found".to_string());
    }

    Ok(results.join(", "))
}

#[tauri::command]
pub async fn get_database_info(db: State<'_, Database>) -> Result<String> {
    let mut info = Vec::new();

    // Get database file size
    let db_path = std::env::current_dir()
        .unwrap()
        .join("data")
        .join("furniture_factory.db");

    if let Ok(metadata) = std::fs::metadata(&db_path) {
        info.push(format!("Database size: {} bytes", metadata.len()));
    }

    // Get SQLite version
    let version: String = sqlx::query_scalar("SELECT sqlite_version()")
        .fetch_one(&db.pool)
        .await?;
    info.push(format!("SQLite version: {}", version));

    // Get database schema version
    let user_version: i64 = sqlx::query_scalar("PRAGMA user_version")
        .fetch_one(&db.pool)
        .await?;
    info.push(format!("Schema version: {}", user_version));

    Ok(info.join(", "))
}

#[tauri::command]
pub async fn test_database_performance(db: State<'_, Database>) -> Result<String> {
    let mut results = Vec::new();
    let start_time = std::time::Instant::now();

    // Test 1: Simple query performance
    let query_start = std::time::Instant::now();
    let _count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM materials")
        .fetch_one(&db.pool)
        .await?;
    let query_duration = query_start.elapsed();
    results.push(format!("Simple query: {}ms", query_duration.as_millis()));

    // Test 2: Complex JOIN query performance
    let join_start = std::time::Instant::now();
    let _join_result = sqlx::query(
        r#"
        SELECT p.name, c.name, COUNT(pm.id) as material_count
        FROM projects p
        LEFT JOIN customers c ON p.customer_id = c.id
        LEFT JOIN project_materials pm ON p.id = pm.project_id
        GROUP BY p.id
        LIMIT 10
        "#
    )
    .fetch_all(&db.pool)
    .await?;
    let join_duration = join_start.elapsed();
    results.push(format!("Complex JOIN: {}ms", join_duration.as_millis()));

    // Test 3: Insert performance
    let insert_start = std::time::Instant::now();
    let timestamp = chrono::Utc::now().timestamp();
    let _insert_result = sqlx::query(
        "INSERT INTO activities (title, description, activity_type) VALUES (?, ?, ?)"
    )
    .bind(format!("Performance Test {}", timestamp))
    .bind("Database performance testing")
    .bind("test")
    .execute(&db.pool)
    .await?;
    let insert_duration = insert_start.elapsed();
    results.push(format!("Insert operation: {}ms", insert_duration.as_millis()));

    let total_duration = start_time.elapsed();
    results.push(format!("Total test time: {}ms", total_duration.as_millis()));

    Ok(results.join(" | "))
}

#[tauri::command]
pub async fn test_database_integrity(db: State<'_, Database>) -> Result<String> {
    let mut results = Vec::new();

    // Test 1: Check for orphaned records
    let orphaned_projects = sqlx::query_scalar::<_, i64>(
        "SELECT COUNT(*) FROM projects WHERE customer_id NOT IN (SELECT id FROM customers)"
    )
    .fetch_one(&db.pool)
    .await?;

    if orphaned_projects == 0 {
        results.push("No orphaned projects found".to_string());
    } else {
        results.push(format!("Warning: {} orphaned projects found", orphaned_projects));
    }

    // Test 2: Check for negative values where they shouldn't exist
    let negative_stock = sqlx::query_scalar::<_, i64>(
        "SELECT COUNT(*) FROM materials WHERE current_stock < 0"
    )
    .fetch_one(&db.pool)
    .await?;

    if negative_stock == 0 {
        results.push("No negative stock values".to_string());
    } else {
        results.push(format!("Warning: {} materials with negative stock", negative_stock));
    }

    // Test 3: Check for duplicate entries
    let duplicate_materials = sqlx::query_scalar::<_, i64>(
        "SELECT COUNT(*) - COUNT(DISTINCT name) FROM materials"
    )
    .fetch_one(&db.pool)
    .await?;

    if duplicate_materials == 0 {
        results.push("No duplicate material names".to_string());
    } else {
        results.push(format!("Warning: {} duplicate material names", duplicate_materials));
    }

    Ok(results.join(" | "))
}
