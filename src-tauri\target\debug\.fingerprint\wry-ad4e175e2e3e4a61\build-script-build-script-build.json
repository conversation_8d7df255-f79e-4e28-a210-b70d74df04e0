{"rustc": 16591470773350601817, "features": "[\"drag-drop\", \"fullscreen\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"os-webview\", \"protocol\", \"soup3\", \"transparent\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11-dl\"]", "declared_features": "[\"default\", \"devtools\", \"drag-drop\", \"fullscreen\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"mac-proxy\", \"os-webview\", \"protocol\", \"serde\", \"soup3\", \"tracing\", \"transparent\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11-dl\"]", "target": 5408242616063297496, "profile": 13277125065115479800, "path": 6133320804996890910, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wry-ad4e175e2e3e4a61\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}