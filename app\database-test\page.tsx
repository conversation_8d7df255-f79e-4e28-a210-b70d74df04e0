"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { databaseTestApi } from "@/lib/tauri-api"
import { Database, CheckCircle, XCircle, Loader2, Info } from "lucide-react"

interface TestResult {
  name: string
  status: 'pending' | 'success' | 'error'
  result?: string
  error?: string
}

export default function DatabaseTestPage() {
  const [tests, setTests] = useState<TestResult[]>([
    { name: "اختبار الاتصال بقاعدة البيانات", status: 'pending' },
    { name: "فحص الجداول", status: 'pending' },
    { name: "فحص البيانات المدرجة", status: 'pending' },
    { name: "اختبار عمليات CRUD", status: 'pending' },
    { name: "اختبار المفاتيح الخارجية", status: 'pending' },
    { name: "معلومات قاعدة البيانات", status: 'pending' },
  ])
  
  const [isRunning, setIsRunning] = useState(false)

  const updateTest = (index: number, updates: Partial<TestResult>) => {
    setTests(prev => prev.map((test, i) => 
      i === index ? { ...test, ...updates } : test
    ))
  }

  const runAllTests = async () => {
    setIsRunning(true)
    
    // Reset all tests
    setTests(prev => prev.map(test => ({ ...test, status: 'pending' as const, result: undefined, error: undefined })))

    const testFunctions = [
      databaseTestApi.testConnection,
      databaseTestApi.testTables,
      databaseTestApi.testDataInsertion,
      databaseTestApi.testCrudOperations,
      databaseTestApi.testForeignKeys,
      databaseTestApi.getDatabaseInfo,
    ]

    for (let i = 0; i < testFunctions.length; i++) {
      try {
        const result = await testFunctions[i]()
        updateTest(i, { 
          status: 'success', 
          result: Array.isArray(result) ? result.join(', ') : result 
        })
      } catch (error) {
        updateTest(i, { 
          status: 'error', 
          error: error instanceof Error ? error.message : 'خطأ غير معروف' 
        })
      }
      
      // Add small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500))
    }
    
    setIsRunning(false)
  }

  const runSingleTest = async (index: number) => {
    const testFunctions = [
      databaseTestApi.testConnection,
      databaseTestApi.testTables,
      databaseTestApi.testDataInsertion,
      databaseTestApi.testCrudOperations,
      databaseTestApi.testForeignKeys,
      databaseTestApi.getDatabaseInfo,
    ]

    updateTest(index, { status: 'pending', result: undefined, error: undefined })

    try {
      const result = await testFunctions[index]()
      updateTest(index, { 
        status: 'success', 
        result: Array.isArray(result) ? result.join(', ') : result 
      })
    } catch (error) {
      updateTest(index, { 
        status: 'error', 
        error: error instanceof Error ? error.message : 'خطأ غير معروف' 
      })
    }
  }

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'pending':
        return <Loader2 className="h-5 w-5 text-gray-400 animate-spin" />
    }
  }

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-500">نجح</Badge>
      case 'error':
        return <Badge variant="destructive">فشل</Badge>
      case 'pending':
        return <Badge variant="secondary">في الانتظار</Badge>
    }
  }

  const successCount = tests.filter(t => t.status === 'success').length
  const errorCount = tests.filter(t => t.status === 'error').length
  const pendingCount = tests.filter(t => t.status === 'pending').length

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="flex items-center gap-3 mb-6">
        <Database className="h-8 w-8 text-blue-500" />
        <div>
          <h1 className="text-3xl font-bold">فحص قاعدة البيانات</h1>
          <p className="text-muted-foreground">اختبار شامل لاتصال قاعدة البيانات وسلامة البيانات</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">إجمالي الاختبارات</p>
                <p className="text-2xl font-bold">{tests.length}</p>
              </div>
              <Info className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">نجح</p>
                <p className="text-2xl font-bold text-green-500">{successCount}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">فشل</p>
                <p className="text-2xl font-bold text-red-500">{errorCount}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">في الانتظار</p>
                <p className="text-2xl font-bold text-gray-500">{pendingCount}</p>
              </div>
              <Loader2 className="h-8 w-8 text-gray-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>تشغيل الاختبارات</CardTitle>
          <CardDescription>
            قم بتشغيل جميع الاختبارات للتحقق من سلامة قاعدة البيانات
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={runAllTests} 
            disabled={isRunning}
            className="w-full"
          >
            {isRunning ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                جاري تشغيل الاختبارات...
              </>
            ) : (
              'تشغيل جميع الاختبارات'
            )}
          </Button>
        </CardContent>
      </Card>

      <div className="space-y-4">
        {tests.map((test, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                  {getStatusIcon(test.status)}
                  <h3 className="font-semibold">{test.name}</h3>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusBadge(test.status)}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => runSingleTest(index)}
                    disabled={isRunning}
                  >
                    تشغيل
                  </Button>
                </div>
              </div>
              
              {test.result && (
                <>
                  <Separator className="my-3" />
                  <div className="bg-green-50 p-3 rounded-md">
                    <p className="text-sm text-green-800 font-medium">النتيجة:</p>
                    <p className="text-sm text-green-700 mt-1">{test.result}</p>
                  </div>
                </>
              )}
              
              {test.error && (
                <>
                  <Separator className="my-3" />
                  <div className="bg-red-50 p-3 rounded-md">
                    <p className="text-sm text-red-800 font-medium">خطأ:</p>
                    <p className="text-sm text-red-700 mt-1">{test.error}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
