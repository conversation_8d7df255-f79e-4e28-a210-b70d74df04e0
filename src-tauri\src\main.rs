#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod database;
mod models;
mod commands;
mod error;

use database::Database;
use tauri::Manager;

#[tokio::main]
async fn main() {
    // Initialize database first
    let db = match Database::new().await {
        Ok(database) => database,
        Err(e) => {
            eprintln!("Failed to initialize database: {}", e);
            std::process::exit(1);
        }
    };

    let result = tauri::Builder::default()
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_notification::init())
        .setup(move |app| {
            // Manage the database instance
            app.manage(db);

            // Show window after setup is complete
            if let Some(window) = app.get_webview_window("main") {
                let _ = window.show();
                let _ = window.set_focus();
            }

            println!("Application setup completed successfully");
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // Material commands
            commands::materials::get_materials,
            commands::materials::create_material,
            commands::materials::update_material,
            commands::materials::delete_material,
            commands::materials::import_materials,
            commands::materials::export_materials,

            // Customer commands
            commands::customers::get_customers,
            commands::customers::create_customer,
            commands::customers::update_customer,
            commands::customers::delete_customer,
            commands::customers::get_customer_by_id,

            // Employee commands
            commands::employees::get_employees,
            commands::employees::create_employee,
            commands::employees::update_employee,
            commands::employees::delete_employee,
            commands::employees::calculate_payroll,

            // Project commands
            commands::projects::get_projects,
            commands::projects::create_project,
            commands::projects::update_project,
            commands::projects::delete_project,
            commands::projects::calculate_project_cost,

            // Payment commands
            commands::payments::get_payments,
            commands::payments::create_payment,
            commands::payments::update_payment_status,
            commands::payments::generate_invoice,

            // Treasury commands
            commands::treasury::get_transactions,
            commands::treasury::create_transaction,
            commands::treasury::get_account_balances,

            // Dashboard commands
            commands::dashboard::get_dashboard_stats,
            commands::dashboard::get_recent_activities,

            // Reports commands
            commands::reports::generate_production_report,
            commands::reports::generate_financial_report,
            commands::reports::export_report,

            // Backup commands
            commands::backup::create_backup,
            commands::backup::restore_backup,
            commands::backup::list_backups,
        ])
        .run(tauri::generate_context!());

    if let Err(e) = result {
        eprintln!("Error while running tauri application: {}", e);
        std::process::exit(1);
    }
}
