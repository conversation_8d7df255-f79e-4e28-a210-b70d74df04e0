"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Users, Plus, ArrowLeft, DollarSign, Clock, Calendar, FileText, Edit, Trash2, Percent } from "lucide-react"
import Link from "next/link"

export default function PayrollManagement() {
  const [employees, setEmployees] = useState([])

  const [newEmployee, setNewEmployee] = useState({
    name: "",
    position: "",
    department: "",
    hourlyRate: "",
    hoursWorked: "",
    overtime: "",
    allowances: "",
    deductions: "",
  })

  const [attendanceRecords, setAttendanceRecords] = useState([])

  const [editingEmployee, setEditingEmployee] = useState(null)
  const [discountEmployee, setDiscountEmployee] = useState(null)
  const [discountAmount, setDiscountAmount] = useState("")
  const [discountReason, setDiscountReason] = useState("")

  const calculateGrossPay = (hourlyRate, hoursWorked, overtime, allowances) => {
    const regularPay = Math.min(hoursWorked, 160) * hourlyRate
    const overtimePay = overtime * hourlyRate * 1.5
    return regularPay + overtimePay + allowances
  }

  const calculateDeductions = (grossPay, deductions, socialInsurance, discount = 0) => {
    const federalTax = grossPay * 0.05 // 5% ضريبة دخل
    const insurance = 37.5 // تأمين طبي ثابت (LYD)
    return federalTax + insurance + deductions + socialInsurance + discount
  }

  const getTotalPayroll = () => {
    return employees.reduce((total, emp) => {
      const grossPay = calculateGrossPay(emp.hourlyRate, emp.hoursWorked, emp.overtime, emp.allowances)
      const deductions = calculateDeductions(grossPay, emp.deductions, emp.socialInsurance, emp.discount)
      return total + (grossPay - deductions)
    }, 0)
  }

  const getAverageHours = () => {
    const totalHours = employees.reduce((total, emp) => total + emp.hoursWorked + emp.overtime, 0)
    return totalHours / employees.length
  }

  const addEmployee = () => {
    const employee = {
      id: employees.length + 1,
      ...newEmployee,
      hourlyRate: Number.parseFloat(newEmployee.hourlyRate),
      hoursWorked: Number.parseFloat(newEmployee.hoursWorked),
      overtime: Number.parseFloat(newEmployee.overtime),
      allowances: Number.parseFloat(newEmployee.allowances),
      deductions: Number.parseFloat(newEmployee.deductions),
      status: "نشط",
      vacationDays: 0,
      socialInsurance: 125.0, // Default LYD
      joinDate: new Date().toISOString().split("T")[0],
      discount: 0,
    }
    setEmployees([...employees, employee])
    setNewEmployee({
      name: "",
      position: "",
      department: "",
      hourlyRate: "",
      hoursWorked: "",
      overtime: "",
      allowances: "",
      deductions: "",
    })
  }

  const updateEmployee = () => {
    setEmployees(
      employees.map((emp) =>
        emp.id === editingEmployee.id
          ? {
              ...editingEmployee,
              hourlyRate: Number.parseFloat(editingEmployee.hourlyRate),
              hoursWorked: Number.parseFloat(editingEmployee.hoursWorked),
              overtime: Number.parseFloat(editingEmployee.overtime),
              allowances: Number.parseFloat(editingEmployee.allowances),
              deductions: Number.parseFloat(editingEmployee.deductions),
            }
          : emp,
      ),
    )
    setEditingEmployee(null)
  }

  const deleteEmployee = (id) => {
    setEmployees(employees.filter((emp) => emp.id !== id))
  }

  const applyDiscount = () => {
    const discount = Number.parseFloat(discountAmount)
    setEmployees(
      employees.map((emp) =>
        emp.id === discountEmployee.id
          ? {
              ...emp,
              discount: discount,
              discountReason: discountReason,
            }
          : emp,
      ),
    )
    setDiscountEmployee(null)
    setDiscountAmount("")
    setDiscountReason("")
  }

  const getVacationSummary = () => {
    const totalVacationDays = employees.reduce((total, emp) => total + emp.vacationDays, 0)
    const avgVacationDays = totalVacationDays / employees.length
    return { total: totalVacationDays, average: avgVacationDays.toFixed(1) }
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center space-x-4 space-x-reverse">
            <Link href="/">
              <Button variant="outline" size="sm" className="border-primary-300 text-primary-700 hover:bg-primary-50">
                <ArrowLeft className="h-4 w-4 ml-2 rtl-flip" />
                العودة للوحة التحكم
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-primary-900">إدارة الرواتب</h1>
              <p className="text-gray-600">إدارة مدفوعات الموظفين والسجلات بالدينار الليبي (د.ل)</p>
            </div>
          </div>

          <Dialog>
            <DialogTrigger asChild>
              <Button className="bg-secondary-600 hover:bg-secondary-700">
                <Plus className="h-4 w-4 ml-2" />
                إضافة موظف
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle className="text-primary-900">إضافة موظف جديد</DialogTitle>
                <DialogDescription className="text-gray-600">أدخل تفاصيل الموظف للرواتب</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name" className="text-primary-900">
                      الاسم الكامل
                    </Label>
                    <Input
                      id="name"
                      value={newEmployee.name}
                      onChange={(e) => setNewEmployee({ ...newEmployee, name: e.target.value })}
                      placeholder="أحمد محمد السالم"
                      className="border-primary-200 focus:border-primary-500"
                    />
                  </div>
                  <div>
                    <Label htmlFor="position" className="text-primary-900">
                      المنصب
                    </Label>
                    <Input
                      id="position"
                      value={newEmployee.position}
                      onChange={(e) => setNewEmployee({ ...newEmployee, position: e.target.value })}
                      placeholder="نجار"
                      className="border-primary-200 focus:border-primary-500"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="department" className="text-primary-900">
                      القسم
                    </Label>
                    <Select
                      value={newEmployee.department}
                      onValueChange={(value) => setNewEmployee({ ...newEmployee, department: value })}
                    >
                      <SelectTrigger className="border-primary-200 focus:border-primary-500">
                        <SelectValue placeholder="اختر القسم" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="الإنتاج">الإنتاج</SelectItem>
                        <SelectItem value="التصميم">التصميم</SelectItem>
                        <SelectItem value="مراقبة الجودة">مراقبة الجودة</SelectItem>
                        <SelectItem value="الإدارة">الإدارة</SelectItem>
                        <SelectItem value="المبيعات">المبيعات</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="hourlyRate" className="text-primary-900">
                      الأجر بالساعة (د.ل)
                    </Label>
                    <Input
                      id="hourlyRate"
                      type="number"
                      step="0.01"
                      value={newEmployee.hourlyRate}
                      onChange={(e) => setNewEmployee({ ...newEmployee, hourlyRate: e.target.value })}
                      placeholder="6.25"
                      className="border-primary-200 focus:border-primary-500"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="allowances" className="text-primary-900">
                      البدلات (د.ل)
                    </Label>
                    <Input
                      id="allowances"
                      type="number"
                      value={newEmployee.allowances}
                      onChange={(e) => setNewEmployee({ ...newEmployee, allowances: e.target.value })}
                      placeholder="125"
                      className="border-primary-200 focus:border-primary-500"
                    />
                  </div>
                  <div>
                    <Label htmlFor="deductions" className="text-primary-900">
                      الخصومات (د.ل)
                    </Label>
                    <Input
                      id="deductions"
                      type="number"
                      value={newEmployee.deductions}
                      onChange={(e) => setNewEmployee({ ...newEmployee, deductions: e.target.value })}
                      placeholder="0"
                      className="border-primary-200 focus:border-primary-500"
                    />
                  </div>
                </div>
                <Button onClick={addEmployee} className="w-full bg-primary-600 hover:bg-primary-700">
                  إضافة الموظف
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="border-2 border-primary-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">إجمالي الموظفين</CardTitle>
              <Users className="h-4 w-4 text-primary-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary-900">{employees.length}</div>
              <p className="text-xs text-gray-500">القوى العاملة النشطة</p>
            </CardContent>
          </Card>

          <Card className="border-2 border-secondary-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">إجمالي الرواتب</CardTitle>
              <DollarSign className="h-4 w-4 text-secondary-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-secondary-900">{getTotalPayroll().toFixed(2)} د.ل</div>
              <p className="text-xs text-gray-500">الراتب الصافي الشهري</p>
            </CardContent>
          </Card>

          <Card className="border-2 border-orange-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">متوسط الساعات</CardTitle>
              <Clock className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-900">{getAverageHours().toFixed(1)}</div>
              <p className="text-xs text-gray-500">ساعة لكل موظف</p>
            </CardContent>
          </Card>

          <Card className="border-2 border-primary-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">أيام الإجازات</CardTitle>
              <Calendar className="h-4 w-4 text-primary-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary-900">{getVacationSummary().total}</div>
              <p className="text-xs text-gray-500">إجمالي أيام الإجازة</p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="payroll" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="payroll" className="flex items-center space-x-2 space-x-reverse">
              <DollarSign className="h-4 w-4" />
              <span>كشف الرواتب</span>
            </TabsTrigger>
            <TabsTrigger value="attendance" className="flex items-center space-x-2 space-x-reverse">
              <Clock className="h-4 w-4" />
              <span>الحضور والانصراف</span>
            </TabsTrigger>
            <TabsTrigger value="vacations" className="flex items-center space-x-2 space-x-reverse">
              <Calendar className="h-4 w-4" />
              <span>الإجازات</span>
            </TabsTrigger>
            <TabsTrigger value="reports" className="flex items-center space-x-2 space-x-reverse">
              <FileText className="h-4 w-4" />
              <span>التقارير</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="payroll">
            <Card className="border-2 border-primary-200">
              <CardHeader className="bg-primary-50">
                <CardTitle className="text-primary-900">كشف رواتب الموظفين</CardTitle>
                <CardDescription className="text-gray-600">
                  حساب الرواتب الشهرية وتفاصيل الموظفين بالدينار الليبي
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-primary-900">الموظف</TableHead>
                      <TableHead className="text-primary-900">المنصب</TableHead>
                      <TableHead className="text-primary-900">الأجر/الساعة</TableHead>
                      <TableHead className="text-primary-900">الساعات</TableHead>
                      <TableHead className="text-primary-900">البدلات</TableHead>
                      <TableHead className="text-primary-900">الخصم</TableHead>
                      <TableHead className="text-primary-900">الراتب الصافي</TableHead>
                      <TableHead className="text-primary-900">الحالة</TableHead>
                      <TableHead className="text-primary-900">الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {employees.map((employee) => {
                      const grossPay = calculateGrossPay(
                        employee.hourlyRate,
                        employee.hoursWorked,
                        employee.overtime,
                        employee.allowances,
                      )
                      const deductions = calculateDeductions(
                        grossPay,
                        employee.deductions,
                        employee.socialInsurance,
                        employee.discount,
                      )
                      const netPay = grossPay - deductions

                      return (
                        <TableRow key={employee.id}>
                          <TableCell className="font-medium text-primary-900">{employee.name}</TableCell>
                          <TableCell className="text-gray-700">{employee.position}</TableCell>
                          <TableCell>{employee.hourlyRate.toFixed(2)} د.ل</TableCell>
                          <TableCell>{employee.hoursWorked}س</TableCell>
                          <TableCell className="text-secondary-600">{employee.allowances} د.ل</TableCell>
                          <TableCell className="text-red-600">
                            {employee.discount > 0 ? `${employee.discount} د.ل` : "-"}
                          </TableCell>
                          <TableCell className="font-bold text-secondary-900">{netPay.toFixed(2)} د.ل</TableCell>
                          <TableCell>
                            <Badge
                              className={
                                employee.status === "نشط"
                                  ? "bg-secondary-100 text-secondary-800"
                                  : "bg-orange-100 text-orange-800"
                              }
                            >
                              {employee.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-1 space-x-reverse">
                              <Dialog>
                                <DialogTrigger asChild>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => setEditingEmployee({ ...employee })}
                                    className="border-primary-300 text-primary-700 hover:bg-primary-50"
                                  >
                                    <Edit className="h-3 w-3" />
                                  </Button>
                                </DialogTrigger>
                                <DialogContent>
                                  <DialogHeader>
                                    <DialogTitle className="text-primary-900">تعديل بيانات الموظف</DialogTitle>
                                  </DialogHeader>
                                  {editingEmployee && (
                                    <div className="grid gap-4 py-4">
                                      <div className="grid grid-cols-2 gap-4">
                                        <div>
                                          <Label className="text-primary-900">الاسم</Label>
                                          <Input
                                            value={editingEmployee.name}
                                            onChange={(e) =>
                                              setEditingEmployee({ ...editingEmployee, name: e.target.value })
                                            }
                                            className="border-primary-200"
                                          />
                                        </div>
                                        <div>
                                          <Label className="text-primary-900">المنصب</Label>
                                          <Input
                                            value={editingEmployee.position}
                                            onChange={(e) =>
                                              setEditingEmployee({ ...editingEmployee, position: e.target.value })
                                            }
                                            className="border-primary-200"
                                          />
                                        </div>
                                      </div>
                                      <div className="grid grid-cols-2 gap-4">
                                        <div>
                                          <Label className="text-primary-900">الأجر/الساعة (د.ل)</Label>
                                          <Input
                                            type="number"
                                            step="0.01"
                                            value={editingEmployee.hourlyRate}
                                            onChange={(e) =>
                                              setEditingEmployee({ ...editingEmployee, hourlyRate: e.target.value })
                                            }
                                            className="border-primary-200"
                                          />
                                        </div>
                                        <div>
                                          <Label className="text-primary-900">البدلات (د.ل)</Label>
                                          <Input
                                            type="number"
                                            value={editingEmployee.allowances}
                                            onChange={(e) =>
                                              setEditingEmployee({ ...editingEmployee, allowances: e.target.value })
                                            }
                                            className="border-primary-200"
                                          />
                                        </div>
                                      </div>
                                      <Button onClick={updateEmployee} className="bg-primary-600 hover:bg-primary-700">
                                        حفظ التغييرات
                                      </Button>
                                    </div>
                                  )}
                                </DialogContent>
                              </Dialog>

                              <Dialog>
                                <DialogTrigger asChild>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => setDiscountEmployee(employee)}
                                    className="border-orange-300 text-orange-700 hover:bg-orange-50"
                                  >
                                    <Percent className="h-3 w-3" />
                                  </Button>
                                </DialogTrigger>
                                <DialogContent>
                                  <DialogHeader>
                                    <DialogTitle className="text-primary-900">تطبيق خصم</DialogTitle>
                                    <DialogDescription>تطبيق خصم على راتب {employee.name}</DialogDescription>
                                  </DialogHeader>
                                  <div className="grid gap-4 py-4">
                                    <div>
                                      <Label className="text-primary-900">مبلغ الخصم (د.ل)</Label>
                                      <Input
                                        type="number"
                                        step="0.01"
                                        value={discountAmount}
                                        onChange={(e) => setDiscountAmount(e.target.value)}
                                        placeholder="0.00"
                                        className="border-primary-200"
                                      />
                                    </div>
                                    <div>
                                      <Label className="text-primary-900">سبب الخصم</Label>
                                      <Input
                                        value={discountReason}
                                        onChange={(e) => setDiscountReason(e.target.value)}
                                        placeholder="سبب الخصم"
                                        className="border-primary-200"
                                      />
                                    </div>
                                    <Button onClick={applyDiscount} className="bg-orange-600 hover:bg-orange-700">
                                      تطبيق الخصم
                                    </Button>
                                  </div>
                                </DialogContent>
                              </Dialog>

                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    className="border-red-300 text-red-700 hover:bg-red-50"
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>تأكيد الحذف</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      هل أنت متأكد من حذف الموظف {employee.name}؟ هذا الإجراء لا يمكن التراجع عنه.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>إلغاء</AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => deleteEmployee(employee.id)}
                                      className="bg-red-600 hover:bg-red-700"
                                    >
                                      حذف
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Other tabs content remains the same but with LYD currency */}
          <TabsContent value="attendance">
            <Card className="border-2 border-primary-200">
              <CardHeader className="bg-primary-50">
                <CardTitle className="text-primary-900">سجل الحضور والانصراف</CardTitle>
                <CardDescription className="text-gray-600">تتبع أوقات حضور وانصراف الموظفين</CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                <p className="text-center text-gray-500 py-8">سيتم تطوير هذا القسم قريباً</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="vacations">
            <Card className="border-2 border-primary-200">
              <CardHeader className="bg-primary-50">
                <CardTitle className="text-primary-900">إدارة الإجازات</CardTitle>
                <CardDescription className="text-gray-600">تتبع إجازات الموظفين والأرصدة المتاحة</CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                <p className="text-center text-gray-500 py-8">سيتم تطوير هذا القسم قريباً</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reports">
            <Card className="border-2 border-primary-200">
              <CardHeader className="bg-primary-50">
                <CardTitle className="text-primary-900">تقارير الرواتب</CardTitle>
                <CardDescription className="text-gray-600">
                  تقارير مفصلة عن الرواتب والتكاليف بالدينار الليبي
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card className="border-2 border-secondary-200">
                    <CardHeader className="bg-secondary-50">
                      <CardTitle className="text-secondary-900">تقرير التكاليف الشهرية</CardTitle>
                    </CardHeader>
                    <CardContent className="pt-6">
                      <div className="space-y-4">
                        <div className="flex justify-between">
                          <span className="text-gray-600">إجمالي الرواتب الأساسية:</span>
                          <span className="font-medium">
                            {employees.reduce((total, emp) => total + emp.hourlyRate * emp.hoursWorked, 0).toFixed(2)}{" "}
                            د.ل
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">إجمالي الساعات الإضافية:</span>
                          <span className="font-medium">
                            {employees
                              .reduce((total, emp) => total + emp.overtime * emp.hourlyRate * 1.5, 0)
                              .toFixed(2)}{" "}
                            د.ل
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">إجمالي البدلات:</span>
                          <span className="font-medium">
                            {employees.reduce((total, emp) => total + emp.allowances, 0).toFixed(2)} د.ل
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">إجمالي الخصومات:</span>
                          <span className="font-medium text-red-600">
                            {employees
                              .reduce(
                                (total, emp) => total + emp.deductions + emp.socialInsurance + emp.discount + 37.5,
                                0,
                              )
                              .toFixed(2)}{" "}
                            د.ل
                          </span>
                        </div>
                        <div className="border-t pt-2 flex justify-between font-bold">
                          <span>صافي الرواتب:</span>
                          <span className="text-secondary-900">{getTotalPayroll().toFixed(2)} د.ل</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-2 border-primary-200">
                    <CardHeader className="bg-primary-50">
                      <CardTitle className="text-primary-900">إحصائيات الموظفين</CardTitle>
                    </CardHeader>
                    <CardContent className="pt-6">
                      <div className="space-y-4">
                        <div className="flex justify-between">
                          <span className="text-gray-600">موظفو الإنتاج:</span>
                          <span className="font-medium">
                            {employees.filter((emp) => emp.department === "الإنتاج").length}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">موظفو التصميم:</span>
                          <span className="font-medium">
                            {employees.filter((emp) => emp.department === "التصميم").length}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">مراقبة الجودة:</span>
                          <span className="font-medium">
                            {employees.filter((emp) => emp.department === "مراقبة الجودة").length}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">متوسط الراتب:</span>
                          <span className="font-medium">{(getTotalPayroll() / employees.length).toFixed(2)} د.ل</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">إجمالي الخصومات:</span>
                          <span className="font-medium text-red-600">
                            {employees.reduce((total, emp) => total + emp.discount, 0).toFixed(2)} د.ل
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Process Payroll Button */}
        <div className="mt-6 flex justify-end space-x-2 space-x-reverse">
          <Button variant="outline" className="border-primary-300 text-primary-700 hover:bg-primary-50">
            <FileText className="h-4 w-4 ml-2" />
            تصدير التقرير
          </Button>
          <Button size="lg" className="bg-secondary-600 hover:bg-secondary-700">
            <DollarSign className="h-4 w-4 ml-2" />
            معالجة الرواتب الشهرية
          </Button>
        </div>
      </div>
    </div>
  )
}
