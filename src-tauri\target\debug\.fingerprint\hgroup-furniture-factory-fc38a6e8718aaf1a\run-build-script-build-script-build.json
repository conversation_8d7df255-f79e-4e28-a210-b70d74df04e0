{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[13239433603405366215, "build_script_build", false, 15265828414939118391], [10755362358622467486, "build_script_build", false, 10391839666771107702], [3834743577069889284, "build_script_build", false, 588137399220213912], [13890802266741835355, "build_script_build", false, 11778675376884222980], [12783828711503588811, "build_script_build", false, 17076110422307882001], [1582828171158827377, "build_script_build", false, 42512887088572202]], "local": [{"RerunIfChanged": {"output": "debug\\build\\hgroup-furniture-factory-fc38a6e8718aaf1a\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}