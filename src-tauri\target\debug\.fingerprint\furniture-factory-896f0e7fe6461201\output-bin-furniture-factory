{"$message_type":"diagnostic","message":"unused imports: `DateTime` and `Utc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\dashboard.rs","byte_start":90,"byte_end":98,"line_start":3,"line_end":3,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\commands\\dashboard.rs","byte_start":100,"byte_end":103,"line_start":3,"line_end":3,"column_start":24,"column_end":27,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":24,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\commands\\dashboard.rs","byte_start":77,"byte_end":106,"line_start":3,"line_end":4,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":1,"highlight_end":29},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `DateTime` and `Utc`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\dashboard.rs:3:14\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse chrono::{DateTime, Utc};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `window`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":890,"byte_end":896,"line_start":33,"line_end":33,"column_start":27,"column_end":33,"is_primary":true,"text":[{"text":"        .on_window_event(|window, event| {","highlight_start":27,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":890,"byte_end":896,"line_start":33,"line_end":33,"column_start":27,"column_end":33,"is_primary":true,"text":[{"text":"        .on_window_event(|window, event| {","highlight_start":27,"highlight_end":33}],"label":null,"suggested_replacement":"_window","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `window`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:33:27\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .on_window_event(|window, event| {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_window`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"method `get_pool` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\database\\mod.rs","byte_start":137,"byte_end":150,"line_start":9,"line_end":9,"column_start":1,"column_end":14,"is_primary":false,"text":[{"text":"impl Database {","highlight_start":1,"highlight_end":14}],"label":"method in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\database\\mod.rs","byte_start":997,"byte_end":1005,"line_start":34,"line_end":34,"column_start":18,"column_end":26,"is_primary":true,"text":[{"text":"    pub async fn get_pool(&self) -> &SqlitePool {","highlight_start":18,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: method `get_pool` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database\\mod.rs:34:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl Database {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethod in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn get_pool(&self) -> &SqlitePool {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variants `Validation` and `NotFound` are never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\error.rs","byte_start":78,"byte_end":83,"line_start":5,"line_end":5,"column_start":10,"column_end":15,"is_primary":false,"text":[{"text":"pub enum Error {","highlight_start":10,"highlight_end":15}],"label":"variants in this enum","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\error.rs","byte_start":187,"byte_end":197,"line_start":10,"line_end":10,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    Validation(String),","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\error.rs","byte_start":243,"byte_end":251,"line_start":13,"line_end":13,"column_start":5,"column_end":13,"is_primary":true,"text":[{"text":"    NotFound(String),","highlight_start":5,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`Error` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variants `Validation` and `NotFound` are never constructed\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\error.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum Error {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mvariants in this enum\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Validation(String),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    NotFound(String),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `Error` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"linking with `link.exe` failed: exit code: 1318","code":null,"level":"error","spans":[],"children":[{"message":"\"C:\\\\Program Files (x86)\\\\Microsoft Visual Studio\\\\2022\\\\BuildTools\\\\VC\\\\Tools\\\\MSVC\\\\14.43.34808\\\\bin\\\\HostX64\\\\x64\\\\link.exe\" \"/NOLOGO\" \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\rustcgH9HAi\\\\symbols.o\" \"<257 object files omitted>\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\src-tauri\\\\target\\\\debug\\\\deps/{libtauri_plugin_notification-4843579de65f357c.rlib,librand-3952a655b754e55a.rlib,librand_chacha-1220c28a4778ba76.rlib,libppv_lite86-038be82bf9331219.rlib,librand_core-a34ff72d9e604d66.rlib,libnotify_rust-1a10c2e6fbc12dd6.rlib,libtauri_winrt_notification-ae50cc31c94b4992.rlib,libquick_xml-53a608e18f69876c.rlib,libtauri_plugin_dialog-bb58c05fa15e5edc.rlib,librfd-641af584e713960a.rlib,libtauri_plugin_fs-7d58a4489e86b5fa.rlib,libtauri_plugin_shell-553c01b0fccdc621.rlib,libopen-3caec0c34434f9c5.rlib,libshared_child-0528d6c9bfd3d7b4.rlib,libos_pipe-ebf35816b53374e0.rlib,libencoding_rs-2f7449f43f814653.rlib,libtauri-afba0bbf9fe9635a.rlib,libdirs-68ff69f7212db4db.rlib,libdirs_sys-a84dd86e8e251f6f.rlib,liboption_ext-4ce027e78700ce6b.rlib,libwindow_vibrancy-8599772fedb27b0a.rlib,libmime-3bb1031fd8f3be52.rlib,libtauri_runtime_wry-a8b43d098014f4b5.rlib,libsoftbuffer-fed62cadeb6a075f.rlib,libwry-7d4612b1872a9e29.rlib,libtao-25d4b263e25d454b.rlib,libwindows_version-aca7e6769b1dcf08.rlib,liblazy_static-64465c89d45fc743.rlib,libwebview2_com-6f47d22995128350.rlib,libwebview2_com_sys-177447427f4c2a92.rlib,libtray_icon-d671ab1e5845d09b.rlib,libmuda-1215998d07c3184f.rlib,libkeyboard_types-b569769ff849448f.rlib,libbitflags-1c73cdaff26d8c95.rlib,libunicode_segmentation-22c602554d266e57.rlib,libcrossbeam_channel-678f015bd8f17e0f.rlib,libtauri_runtime-1c99e9a4fdd25e9d.rlib,libcookie-c2cacb6b793754c4.rlib,libtime-dff7d1d006588fb2.rlib,libtime_core-ceeef9debcdb7c5c.rlib,libnum_conv-6f9522700ce2bcbc.rlib,libderanged-fe6ed80e0f3c45e0.rlib,libpowerfmt-b75b3af7335343f0.rlib,libwindows-f59412ed0b07ebe8.rlib,libwindows_collections-fd5f4c1ab208b56f.rlib,libwindows_future-93fc5cb2d75bb89c.rlib,libwindows_core-63108d3902eb7e28.rlib,libwindows_strings-f6aa4139ce9371d4.rlib,libwindows_result-d70826bf967d69b9.rlib,libdpi-ec1a9c7a8559b3f9.rlib,libserialize_to_javascript-329f492d9c1281f5.rlib,libraw_window_handle-9995726761d136a2.rlib,libtauri_utils-f257d7af5ff0e98f.rlib,libdunce-8b32ffa7a1c0ab00.rlib,libinfer-b594711e3d9772d9.rlib,libcfb-2eb30d9b43a4c5a2.rlib,libbrotli-1a6c027b72609cd6.rlib,libbrotli_decompressor-ea1a4c115b5d9526.rlib,liballoc_stdlib-0d887181dc7307b7.rlib,liballoc_no_stdlib-52bb428441e88a0f.rlib,liburlpattern-411cd7a5443cab91.rlib,libunic_ucd_ident-2cdc054baeb4ca6e.rlib,libunic_ucd_version-43c37e45cd62b5dd.rlib,libunic_common-0fc26586c7548755.rlib,libunic_char_property-9b3ae364ce6c445d.rlib,libunic_char_range-2dbdc0274dfe22c3.rlib,libregex-b2714a46511450c7.rlib,libregex_automata-a2d94b92046ed442.rlib,libaho_corasick-c3eac4f8983bb1cc.rlib,libregex_syntax-8e4b3e42b379f069.rlib,libglob-66d90e1b3024e0d2.rlib,libwalkdir-3889b46572e8e87f.rlib,libsame_file-29fdc6ca4c5cc783.rlib,libwinapi_util-e25dbac706cf4896.rlib,libkuchikiki-505e21025ca5556a.rlib,libselectors-65a56e3d9b4dc244.rlib,libthin_slice-1c680b3befc0f3c1.rlib,libservo_arc-23bd0b8648b61b09.rlib,libnodrop-354f11a001721503.rlib,libfxhash-70033ea95cb2d2d1.rlib,libbyteorder-5b095da27a552c36.rlib,libbitflags-d523da4f21aaa688.rlib,libcssparser-c75095ca29490ef5.rlib,libitoa-25e0c52ca8739e25.rlib,libdtoa_short-d08958ef42447c0e.rlib,libdtoa-090a8444ff565d3e.rlib,libphf-8e4758aa5bd597c2.rlib,libphf_shared-b35c098d44636d9c.rlib,libindexmap-7a9a621eb985e5ab.rlib,libhashbrown-13a2aacd784e4a85.rlib,libmatches-9040439580a9d25b.rlib,libhtml5ever-b200daa6c5b033bf.rlib,libmarkup5ever-c9c649261895e6ca.rlib,libstring_cache-520a503493fb5029.rlib,libprecomputed_hash-c71986671983f682.rlib,libphf-eee94ff39e1f2b00.rlib,libphf_shared-93cf46f79133b165.rlib,libsiphasher-433e8bf4c3557c0c.rlib,libtendril-78d6ab46bf84fb38.rlib,libutf8-70b109613adbd7d4.rlib,libfutf-2ddf684cc74cdd03.rlib,libdebug_unreachable-0633c3aa37a9e3e8.rlib,libmac-527bc77beca32595.rlib,libjson_patch-b92eb85b7d5d621f.rlib,libjsonptr-c529e3b771d66a9c.rlib,libserde_with-62b60acc4ecbc4c9.rlib,libhttp-7f2baf42716fc19b.rlib,libfnv-667ad463444f5737.rlib,libphf-31c47e1f746fc306.rlib,libphf_shared-a8a234e76fa6a10e.rlib,libsiphasher-044e9ebd3ed8b044.rlib,libtoml-d1904f9f7eef7b55.rlib,libtoml_edit-ac54ba0e18391337.rlib,libserde_spanned-55d67cf28355f12c.rlib,libwinnow-8c12f61d6cc514bd.rlib,libtoml_write-2a774931ad798b99.rlib,libtoml_datetime-ea8a206e47b9f183.rlib,libserde_untagged-c60650120f97ed8e.rlib,liberased_serde-c7d15eb3a2373844.rlib,libtypeid-f3964fa618ec401c.rlib,libthiserror-0585c4a178d71595.rlib,libanyhow-45e0b382ef5d673a.rlib,libsemver-db75558d2fca9321.rlib,libsqlx-e7ffb2d365dc6e2e.rlib,libsqlx_sqlite-e42a3dc7b80b5674.rlib,libfutures_executor-20548feacc042b07.rlib,libflume-be486bd82246fcaf.rlib,libspin-974c19199d6962b2.rlib,liburlencoding-ba47bb4fc84d074f.rlib,libfutures_channel-0a6b472bf93b5419.rlib,libfutures_intrusive-68307e23a0b187ca.rlib,liblibsqlite3_sys-237549ac0a274f05.rlib,libatoi-18ffa52482d22ebc.rlib,libsqlx_core-648ef309ab8d69ea.rlib,librustls_pemfile-39df54daad9ee215.rlib,libbase64-d0b0cbc09343ea6d.rlib,libwebpki_roots-bf6b37a6db000c4a.rlib,libtracing-1816a5e70bc5a850.rlib,libtracing_core-55a74d3a6bdd972d.rlib,libthiserror-6449de14a3b59852.rlib,libindexmap-908caedf8d2758e4.rlib,libequivalent-ee194a03ea6282a7.rlib,libhashbrown-16912f9a883c39a9.rlib,libsha2-1a74fd1771efaedd.rlib,libcpufeatures-9f86febff85e3916.rlib,libdigest-745273d305235435.rlib,libblock_buffer-2b67e913104e84f5.rlib,libcrypto_common-d029c573fcecc793.rlib,libgeneric_array-d6507b7a17c34704.rlib,libtypenum-6be563c5bbe02e5a.rlib,librustls-f4a141a6465524c2.rlib,libsct-1d3b47283b7b7cfb.rlib,libwebpki-ac45cb094be3c7c7.rlib,libring-49d7f93f92d701b5.rlib,libgetrandom-0acf4db17f8329b8.rlib,libuntrusted-36e9d8747e834761.rlib,libsqlformat-479a946531c18d53.rlib,libunicode_categories-74af1be88a34d1cb.rlib,libnom-de511a5c073dddf2.rlib,libtokio-42f466f8ae37408b.rlib,libsocket2-bcbc3550448de205.rlib,libwindows_sys-a66020db11e6b2f1.rlib,libmio-9382a4f9df9c4c48.rlib,libwindows_sys-3f8ae797fa47cd32.rlib,libparking_lot-7565345fc200346e.rlib,libparking_lot_core-f73536215fd7608a.rlib,libwindows_targets-832063ff1bd69dfe.rlib,liblock_api-cb8004615e3f4ee2.rlib,libscopeguard-0ea152da1c635355.rlib,libbytes-061af80b88a4e9a9.rlib,libhashlink-11596e1b1ad746c3.rlib,libhashbrown-f53acc76331d966a.rlib,libahash-c2b5f59c1e4f7cf8.rlib,libonce_cell-f8671f094b8884ac.rlib,libzerocopy-e8ac166101d36229.rlib,liballocator_api2-ce4fbb7a8d6c8346.rlib,libchrono-91506aea12144849.rlib,libwindows_link-c671848fa1bbbd80.rlib,libnum_traits-24f2e0771360e3b3.rlib,libuuid-14214ad7e7a82364.rlib,libgetrandom-9f36de977d784c91.rlib,libcfg_if-211458c1ab1808ad.rlib,libserde_json-3d493f1a9f19970e.rlib,libitoa-82e50a6d74a7b471.rlib,libryu-d2a7ab7791ce36e4.rlib,liburl-6a071bf3708c8e67.rlib,libidna-724ad529922394c8.rlib,libutf8_iter-219f97cfa0cacdb0.rlib,libidna_adapter-4ec47fa60f9691b1.rlib,libicu_properties-df9ec633add75d47.rlib,libicu_properties_data-029ce1b7a571026e.rlib,libicu_normalizer-d67437c8a7292b08.rlib,libsmallvec-8852ba454fa13744.rlib,libicu_normalizer_data-670c728bd730785e.rlib,libicu_collections-8db6265ed542047f.rlib,libpotential_utf-32ace93ee15ba8bc.rlib,libicu_provider-6e88a99a250e1442.rlib,libicu_locale_core-a05837cd36326439.rlib,libtinystr-9c3c4f9dae828989.rlib,liblitemap-c41745663806bcdd.rlib,libwriteable-be1dd466d0d1abe7.rlib,libzerovec-22e1f08174c22c69.rlib,libzerotrie-26f7ccee6ef64baa.rlib,libyoke-cd5464726a984c98.rlib,libstable_deref_trait-2e50abd872724d2c.rlib,libzerofrom-3f9cae3acfaa2001.rlib,libform_urlencoded-b931625d83505a8e.rlib,libpercent_encoding-fdfa14da79359144.rlib,liblog-9a9366ba6566a102.rlib,libcrossbeam_queue-38ce26d2ecbf9997.rlib,libcrossbeam_utils-d941eacdc82fad15.rlib,libeither-d05b0785da4c7282.rlib,libserde-b0a41917da6d50ec.rlib,libevent_listener-771062ac28bcbf28.rlib,libfutures_util-63945e6fb5780716.rlib,libmemchr-abe59992233c7b36.rlib,libfutures_io-e67bff27bf10377f.rlib,libslab-86f094d9a38e71cc.rlib,libpin_project_lite-62b92fb748c95e17.rlib,libfutures_sink-65f463e0c17257ad.rlib,libfutures_task-0df837223e696ec8.rlib,libpin_utils-37a54f89346459a4.rlib,libfutures_core-a9f2573d1b50e568.rlib}.rlib\" \"<sysroot>\\\\lib\\\\rustlib\\\\x86_64-pc-windows-msvc\\\\lib/{libstd-*,libpanic_unwind-*,libwindows_targets-*,librustc_demangle-*,libstd_detect-*,libhashbrown-*,librustc_std_workspace_alloc-*,libunwind-*,libcfg_if-*,liballoc-*,librustc_std_workspace_core-*,libcore-*,libcompiler_builtins-*}.rlib\" \"shell32.lib\" \"ole32.lib\" \"bcrypt.lib\" \"advapi32.lib\" \"C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1949cf8c6b5b557f\\\\windows_x86_64_msvc-0.52.6\\\\lib\\\\windows.0.52.0.lib\" \"C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1949cf8c6b5b557f\\\\windows_x86_64_msvc-0.52.6\\\\lib\\\\windows.0.52.0.lib\" \"C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1949cf8c6b5b557f\\\\windows_x86_64_msvc-0.52.6\\\\lib\\\\windows.0.52.0.lib\" \"kernel32.lib\" \"kernel32.lib\" \"ntdll.lib\" \"userenv.lib\" \"ws2_32.lib\" \"dbghelp.lib\" \"/defaultlib:msvcrt\" \"/NXCOMPAT\" \"/LIBPATH:C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1949cf8c6b5b557f\\\\windows_x86_64_msvc-0.52.6\\\\lib\" \"/LIBPATH:C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\src-tauri\\\\target\\\\debug\\\\build\\\\ring-7cff8ad750c4d15e\\\\out\" \"/LIBPATH:C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\src-tauri\\\\target\\\\debug\\\\build\\\\libsqlite3-sys-d007135c9213865f\\\\out\" \"/LIBPATH:C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\src-tauri\\\\target\\\\debug\\\\build\\\\webview2-com-sys-9010636fc9b0c223\\\\out\\\\x64\" \"/OUT:C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\src-tauri\\\\target\\\\debug\\\\deps\\\\furniture_factory.exe\" \"/OPT:REF,NOICF\" \"/DEBUG\" \"/PDBALTPATH:%_PDB%\" \"/NATVIS:<sysroot>\\\\lib\\\\rustlib\\\\etc\\\\intrinsic.natvis\" \"/NATVIS:<sysroot>\\\\lib\\\\rustlib\\\\etc\\\\liballoc.natvis\" \"/NATVIS:<sysroot>\\\\lib\\\\rustlib\\\\etc\\\\libcore.natvis\" \"/NATVIS:<sysroot>\\\\lib\\\\rustlib\\\\etc\\\\libstd.natvis\" \"/NATVIS:C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\rustcgH9HAi\\\\furniture_factory-0.natvis\" \"/NATVIS:C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\rustcgH9HAi\\\\furniture_factory-1.natvis\" \"/NATVIS:C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\rustcgH9HAi\\\\furniture_factory-2.natvis\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\src-tauri\\\\target\\\\debug\\\\build\\\\furniture-factory-f38f1c5d2701d798\\\\out\\\\resource.lib\"","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"some arguments are omitted. use `--verbose` to show all linker arguments","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"LINK : fatal error LNK1318: Unexpected PDB error; LIMIT (12) 'C:\\Users\\<USER>\\Desktop\\New folder\\src-tauri\\target\\debug\\deps\\furniture_factory.pdb'\r\n","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: linking with `link.exe` failed: exit code: 1318\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: \"C:\\\\Program Files (x86)\\\\Microsoft Visual Studio\\\\2022\\\\BuildTools\\\\VC\\\\Tools\\\\MSVC\\\\14.43.34808\\\\bin\\\\HostX64\\\\x64\\\\link.exe\" \"/NOLOGO\" \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\rustcgH9HAi\\\\symbols.o\" \"<257 object files omitted>\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\src-tauri\\\\target\\\\debug\\\\deps/{libtauri_plugin_notification-4843579de65f357c.rlib,librand-3952a655b754e55a.rlib,librand_chacha-1220c28a4778ba76.rlib,libppv_lite86-038be82bf9331219.rlib,librand_core-a34ff72d9e604d66.rlib,libnotify_rust-1a10c2e6fbc12dd6.rlib,libtauri_winrt_notification-ae50cc31c94b4992.rlib,libquick_xml-53a608e18f69876c.rlib,libtauri_plugin_dialog-bb58c05fa15e5edc.rlib,librfd-641af584e713960a.rlib,libtauri_plugin_fs-7d58a4489e86b5fa.rlib,libtauri_plugin_shell-553c01b0fccdc621.rlib,libopen-3caec0c34434f9c5.rlib,libshared_child-0528d6c9bfd3d7b4.rlib,libos_pipe-ebf35816b53374e0.rlib,libencoding_rs-2f7449f43f814653.rlib,libtauri-afba0bbf9fe9635a.rlib,libdirs-68ff69f7212db4db.rlib,libdirs_sys-a84dd86e8e251f6f.rlib,liboption_ext-4ce027e78700ce6b.rlib,libwindow_vibrancy-8599772fedb27b0a.rlib,libmime-3bb1031fd8f3be52.rlib,libtauri_runtime_wry-a8b43d098014f4b5.rlib,libsoftbuffer-fed62cadeb6a075f.rlib,libwry-7d4612b1872a9e29.rlib,libtao-25d4b263e25d454b.rlib,libwindows_version-aca7e6769b1dcf08.rlib,liblazy_static-64465c89d45fc743.rlib,libwebview2_com-6f47d22995128350.rlib,libwebview2_com_sys-177447427f4c2a92.rlib,libtray_icon-d671ab1e5845d09b.rlib,libmuda-1215998d07c3184f.rlib,libkeyboard_types-b569769ff849448f.rlib,libbitflags-1c73cdaff26d8c95.rlib,libunicode_segmentation-22c602554d266e57.rlib,libcrossbeam_channel-678f015bd8f17e0f.rlib,libtauri_runtime-1c99e9a4fdd25e9d.rlib,libcookie-c2cacb6b793754c4.rlib,libtime-dff7d1d006588fb2.rlib,libtime_core-ceeef9debcdb7c5c.rlib,libnum_conv-6f9522700ce2bcbc.rlib,libderanged-fe6ed80e0f3c45e0.rlib,libpowerfmt-b75b3af7335343f0.rlib,libwindows-f59412ed0b07ebe8.rlib,libwindows_collections-fd5f4c1ab208b56f.rlib,libwindows_future-93fc5cb2d75bb89c.rlib,libwindows_core-63108d3902eb7e28.rlib,libwindows_strings-f6aa4139ce9371d4.rlib,libwindows_result-d70826bf967d69b9.rlib,libdpi-ec1a9c7a8559b3f9.rlib,libserialize_to_javascript-329f492d9c1281f5.rlib,libraw_window_handle-9995726761d136a2.rlib,libtauri_utils-f257d7af5ff0e98f.rlib,libdunce-8b32ffa7a1c0ab00.rlib,libinfer-b594711e3d9772d9.rlib,libcfb-2eb30d9b43a4c5a2.rlib,libbrotli-1a6c027b72609cd6.rlib,libbrotli_decompressor-ea1a4c115b5d9526.rlib,liballoc_stdlib-0d887181dc7307b7.rlib,liballoc_no_stdlib-52bb428441e88a0f.rlib,liburlpattern-411cd7a5443cab91.rlib,libunic_ucd_ident-2cdc054baeb4ca6e.rlib,libunic_ucd_version-43c37e45cd62b5dd.rlib,libunic_common-0fc26586c7548755.rlib,libunic_char_property-9b3ae364ce6c445d.rlib,libunic_char_range-2dbdc0274dfe22c3.rlib,libregex-b2714a46511450c7.rlib,libregex_automata-a2d94b92046ed442.rlib,libaho_corasick-c3eac4f8983bb1cc.rlib,libregex_syntax-8e4b3e42b379f069.rlib,libglob-66d90e1b3024e0d2.rlib,libwalkdir-3889b46572e8e87f.rlib,libsame_file-29fdc6ca4c5cc783.rlib,libwinapi_util-e25dbac706cf4896.rlib,libkuchikiki-505e21025ca5556a.rlib,libselectors-65a56e3d9b4dc244.rlib,libthin_slice-1c680b3befc0f3c1.rlib,libservo_arc-23bd0b8648b61b09.rlib,libnodrop-354f11a001721503.rlib,libfxhash-70033ea95cb2d2d1.rlib,libbyteorder-5b095da27a552c36.rlib,libbitflags-d523da4f21aaa688.rlib,libcssparser-c75095ca29490ef5.rlib,libitoa-25e0c52ca8739e25.rlib,libdtoa_short-d08958ef42447c0e.rlib,libdtoa-090a8444ff565d3e.rlib,libphf-8e4758aa5bd597c2.rlib,libphf_shared-b35c098d44636d9c.rlib,libindexmap-7a9a621eb985e5ab.rlib,libhashbrown-13a2aacd784e4a85.rlib,libmatches-9040439580a9d25b.rlib,libhtml5ever-b200daa6c5b033bf.rlib,libmarkup5ever-c9c649261895e6ca.rlib,libstring_cache-520a503493fb5029.rlib,libprecomputed_hash-c71986671983f682.rlib,libphf-eee94ff39e1f2b00.rlib,libphf_shared-93cf46f79133b165.rlib,libsiphasher-433e8bf4c3557c0c.rlib,libtendril-78d6ab46bf84fb38.rlib,libutf8-70b109613adbd7d4.rlib,libfutf-2ddf684cc74cdd03.rlib,libdebug_unreachable-0633c3aa37a9e3e8.rlib,libmac-527bc77beca32595.rlib,libjson_patch-b92eb85b7d5d621f.rlib,libjsonptr-c529e3b771d66a9c.rlib,libserde_with-62b60acc4ecbc4c9.rlib,libhttp-7f2baf42716fc19b.rlib,libfnv-667ad463444f5737.rlib,libphf-31c47e1f746fc306.rlib,libphf_shared-a8a234e76fa6a10e.rlib,libsiphasher-044e9ebd3ed8b044.rlib,libtoml-d1904f9f7eef7b55.rlib,libtoml_edit-ac54ba0e18391337.rlib,libserde_spanned-55d67cf28355f12c.rlib,libwinnow-8c12f61d6cc514bd.rlib,libtoml_write-2a774931ad798b99.rlib,libtoml_datetime-ea8a206e47b9f183.rlib,libserde_untagged-c60650120f97ed8e.rlib,liberased_serde-c7d15eb3a2373844.rlib,libtypeid-f3964fa618ec401c.rlib,libthiserror-0585c4a178d71595.rlib,libanyhow-45e0b382ef5d673a.rlib,libsemver-db75558d2fca9321.rlib,libsqlx-e7ffb2d365dc6e2e.rlib,libsqlx_sqlite-e42a3dc7b80b5674.rlib,libfutures_executor-20548feacc042b07.rlib,libflume-be486bd82246fcaf.rlib,libspin-974c19199d6962b2.rlib,liburlencoding-ba47bb4fc84d074f.rlib,libfutures_channel-0a6b472bf93b5419.rlib,libfutures_intrusive-68307e23a0b187ca.rlib,liblibsqlite3_sys-237549ac0a274f05.rlib,libatoi-18ffa52482d22ebc.rlib,libsqlx_core-648ef309ab8d69ea.rlib,librustls_pemfile-39df54daad9ee215.rlib,libbase64-d0b0cbc09343ea6d.rlib,libwebpki_roots-bf6b37a6db000c4a.rlib,libtracing-1816a5e70bc5a850.rlib,libtracing_core-55a74d3a6bdd972d.rlib,libthiserror-6449de14a3b59852.rlib,libindexmap-908caedf8d2758e4.rlib,libequivalent-ee194a03ea6282a7.rlib,libhashbrown-16912f9a883c39a9.rlib,libsha2-1a74fd1771efaedd.rlib,libcpufeatures-9f86febff85e3916.rlib,libdigest-745273d305235435.rlib,libblock_buffer-2b67e913104e84f5.rlib,libcrypto_common-d029c573fcecc793.rlib,libgeneric_array-d6507b7a17c34704.rlib,libtypenum-6be563c5bbe02e5a.rlib,librustls-f4a141a6465524c2.rlib,libsct-1d3b47283b7b7cfb.rlib,libwebpki-ac45cb094be3c7c7.rlib,libring-49d7f93f92d701b5.rlib,libgetrandom-0acf4db17f8329b8.rlib,libuntrusted-36e9d8747e834761.rlib,libsqlformat-479a946531c18d53.rlib,libunicode_categories-74af1be88a34d1cb.rlib,libnom-de511a5c073dddf2.rlib,libtokio-42f466f8ae37408b.rlib,libsocket2-bcbc3550448de205.rlib,libwindows_sys-a66020db11e6b2f1.rlib,libmio-9382a4f9df9c4c48.rlib,libwindows_sys-3f8ae797fa47cd32.rlib,libparking_lot-7565345fc200346e.rlib,libparking_lot_core-f73536215fd7608a.rlib,libwindows_targets-832063ff1bd69dfe.rlib,liblock_api-cb8004615e3f4ee2.rlib,libscopeguard-0ea152da1c635355.rlib,libbytes-061af80b88a4e9a9.rlib,libhashlink-11596e1b1ad746c3.rlib,libhashbrown-f53acc76331d966a.rlib,libahash-c2b5f59c1e4f7cf8.rlib,libonce_cell-f8671f094b8884ac.rlib,libzerocopy-e8ac166101d36229.rlib,liballocator_api2-ce4fbb7a8d6c8346.rlib,libchrono-91506aea12144849.rlib,libwindows_link-c671848fa1bbbd80.rlib,libnum_traits-24f2e0771360e3b3.rlib,libuuid-14214ad7e7a82364.rlib,libgetrandom-9f36de977d784c91.rlib,libcfg_if-211458c1ab1808ad.rlib,libserde_json-3d493f1a9f19970e.rlib,libitoa-82e50a6d74a7b471.rlib,libryu-d2a7ab7791ce36e4.rlib,liburl-6a071bf3708c8e67.rlib,libidna-724ad529922394c8.rlib,libutf8_iter-219f97cfa0cacdb0.rlib,libidna_adapter-4ec47fa60f9691b1.rlib,libicu_properties-df9ec633add75d47.rlib,libicu_properties_data-029ce1b7a571026e.rlib,libicu_normalizer-d67437c8a7292b08.rlib,libsmallvec-8852ba454fa13744.rlib,libicu_normalizer_data-670c728bd730785e.rlib,libicu_collections-8db6265ed542047f.rlib,libpotential_utf-32ace93ee15ba8bc.rlib,libicu_provider-6e88a99a250e1442.rlib,libicu_locale_core-a05837cd36326439.rlib,libtinystr-9c3c4f9dae828989.rlib,liblitemap-c41745663806bcdd.rlib,libwriteable-be1dd466d0d1abe7.rlib,libzerovec-22e1f08174c22c69.rlib,libzerotrie-26f7ccee6ef64baa.rlib,libyoke-cd5464726a984c98.rlib,libstable_deref_trait-2e50abd872724d2c.rlib,libzerofrom-3f9cae3acfaa2001.rlib,libform_urlencoded-b931625d83505a8e.rlib,libpercent_encoding-fdfa14da79359144.rlib,liblog-9a9366ba6566a102.rlib,libcrossbeam_queue-38ce26d2ecbf9997.rlib,libcrossbeam_utils-d941eacdc82fad15.rlib,libeither-d05b0785da4c7282.rlib,libserde-b0a41917da6d50ec.rlib,libevent_listener-771062ac28bcbf28.rlib,libfutures_util-63945e6fb5780716.rlib,libmemchr-abe59992233c7b36.rlib,libfutures_io-e67bff27bf10377f.rlib,libslab-86f094d9a38e71cc.rlib,libpin_project_lite-62b92fb748c95e17.rlib,libfutures_sink-65f463e0c17257ad.rlib,libfutures_task-0df837223e696ec8.rlib,libpin_utils-37a54f89346459a4.rlib,libfutures_core-a9f2573d1b50e568.rlib}.rlib\" \"<sysroot>\\\\lib\\\\rustlib\\\\x86_64-pc-windows-msvc\\\\lib/{libstd-*,libpanic_unwind-*,libwindows_targets-*,librustc_demangle-*,libstd_detect-*,libhashbrown-*,librustc_std_workspace_alloc-*,libunwind-*,libcfg_if-*,liballoc-*,librustc_std_workspace_core-*,libcore-*,libcompiler_builtins-*}.rlib\" \"shell32.lib\" \"ole32.lib\" \"bcrypt.lib\" \"advapi32.lib\" \"C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1949cf8c6b5b557f\\\\windows_x86_64_msvc-0.52.6\\\\lib\\\\windows.0.52.0.lib\" \"C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1949cf8c6b5b557f\\\\windows_x86_64_msvc-0.52.6\\\\lib\\\\windows.0.52.0.lib\" \"C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1949cf8c6b5b557f\\\\windows_x86_64_msvc-0.52.6\\\\lib\\\\windows.0.52.0.lib\" \"kernel32.lib\" \"kernel32.lib\" \"ntdll.lib\" \"userenv.lib\" \"ws2_32.lib\" \"dbghelp.lib\" \"/defaultlib:msvcrt\" \"/NXCOMPAT\" \"/LIBPATH:C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1949cf8c6b5b557f\\\\windows_x86_64_msvc-0.52.6\\\\lib\" \"/LIBPATH:C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\src-tauri\\\\target\\\\debug\\\\build\\\\ring-7cff8ad750c4d15e\\\\out\" \"/LIBPATH:C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\src-tauri\\\\target\\\\debug\\\\build\\\\libsqlite3-sys-d007135c9213865f\\\\out\" \"/LIBPATH:C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\src-tauri\\\\target\\\\debug\\\\build\\\\webview2-com-sys-9010636fc9b0c223\\\\out\\\\x64\" \"/OUT:C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\src-tauri\\\\target\\\\debug\\\\deps\\\\furniture_factory.exe\" \"/OPT:REF,NOICF\" \"/DEBUG\" \"/PDBALTPATH:%_PDB%\" \"/NATVIS:<sysroot>\\\\lib\\\\rustlib\\\\etc\\\\intrinsic.natvis\" \"/NATVIS:<sysroot>\\\\lib\\\\rustlib\\\\etc\\\\liballoc.natvis\" \"/NATVIS:<sysroot>\\\\lib\\\\rustlib\\\\etc\\\\libcore.natvis\" \"/NATVIS:<sysroot>\\\\lib\\\\rustlib\\\\etc\\\\libstd.natvis\" \"/NATVIS:C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\rustcgH9HAi\\\\furniture_factory-0.natvis\" \"/NATVIS:C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\rustcgH9HAi\\\\furniture_factory-1.natvis\" \"/NATVIS:C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\rustcgH9HAi\\\\furniture_factory-2.natvis\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\src-tauri\\\\target\\\\debug\\\\build\\\\furniture-factory-f38f1c5d2701d798\\\\out\\\\resource.lib\"\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: some arguments are omitted. use `--verbose` to show all linker arguments\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: LINK : fatal error LNK1318: Unexpected PDB error; LIMIT (12) 'C:\\Users\\<USER>\\Desktop\\New folder\\src-tauri\\target\\debug\\deps\\furniture_factory.pdb'␍\u001b[0m\n\u001b[0m          \u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error; 4 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 1 previous error; 4 warnings emitted\u001b[0m\n\n"}
